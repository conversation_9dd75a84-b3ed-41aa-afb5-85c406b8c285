import request from '@/utils/request'

// 查询剧本、店家收藏列表
export function listCollect(query) {
  return request({
    url: '/drama/collect/list',
    method: 'get',
    params: query
  })
}

// 查询剧本、店家收藏详细
export function getCollect(collectId) {
  return request({
    url: '/drama/collect/' + collectId,
    method: 'get'
  })
}

// 新增剧本、店家收藏
export function addCollect(data) {
  return request({
    url: '/drama/collect',
    method: 'post',
    data: data
  })
}

// 修改剧本、店家收藏
export function updateCollect(data) {
  return request({
    url: '/drama/collect',
    method: 'put',
    data: data
  })
}

// 删除剧本、店家收藏
export function delCollect(collectId) {
  return request({
    url: '/drama/collect/' + collectId,
    method: 'delete'
  })
}
