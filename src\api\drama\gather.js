import request from '@/utils/request'

// 查询拼场列表
export function listGather(query) {
  return request({
    url: '/drama/gather/list',
    method: 'get',
    params: query
  })
}

// 查询拼场详细
export function getGather(gatherId) {
  return request({
    url: '/drama/gather/' + gatherId,
    method: 'get'
  })
}

// 新增拼场
export function addGather(data) {
  return request({
    url: '/drama/gather',
    method: 'post',
    data: data
  })
}

// 修改拼场
export function updateGather(data) {
  return request({
    url: '/drama/gather',
    method: 'put',
    data: data
  })
}

// 删除拼场
export function delGather(gatherId) {
  return request({
    url: '/drama/gather/' + gatherId,
    method: 'delete'
  })
}
