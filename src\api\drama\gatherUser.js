import request from '@/utils/request'

// 查询拼场玩家关联列表
export function listGatherUser(query) {
  return request({
    url: '/drama/gatherUser/list',
    method: 'get',
    params: query
  })
}

// 查询拼场玩家关联详细
export function getGatherUser(id) {
  return request({
    url: '/drama/gatherUser/' + id,
    method: 'get'
  })
}

// 新增拼场玩家关联
export function addGatherUser(data) {
  return request({
    url: '/drama/gatherUser',
    method: 'post',
    data: data
  })
}

// 修改拼场玩家关联
export function updateGatherUser(data) {
  return request({
    url: '/drama/gatherUser',
    method: 'put',
    data: data
  })
}

// 删除拼场玩家关联
export function delGatherUser(id) {
  return request({
    url: '/drama/gatherUser/' + id,
    method: 'delete'
  })
}
