import request from '@/utils/request'

// 查询标签关联列表
export function listRelation(query) {
  return request({
    url: '/drama/relation/list',
    method: 'get',
    params: query
  })
}

// 查询标签关联详细
export function getRelation(id) {
  return request({
    url: '/drama/relation/' + id,
    method: 'get'
  })
}

// 新增标签关联
export function addRelation(data) {
  return request({
    url: '/drama/relation',
    method: 'post',
    data: data
  })
}

// 修改标签关联
export function updateRelation(data) {
  return request({
    url: '/drama/relation',
    method: 'put',
    data: data
  })
}

// 删除标签关联
export function delRelation(id) {
  return request({
    url: '/drama/relation/' + id,
    method: 'delete'
  })
}
