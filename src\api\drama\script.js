import request from '@/utils/request'

// 查询剧本列表
export function listScript(query) {
  return request({
    url: '/drama/script/list',
    method: 'get',
    params: query
  })
}

// 查询剧本详细
export function getScript(scriptId) {
  return request({
    url: '/drama/script/' + scriptId,
    method: 'get'
  })
}

// 新增剧本
export function addScript(data) {
  return request({
    url: '/drama/script',
    method: 'post',
    data: data
  })
}

// 修改剧本
export function updateScript(data) {
  return request({
    url: '/drama/script',
    method: 'put',
    data: data
  })
}

// 删除剧本
export function delScript(scriptId) {
  return request({
    url: '/drama/script/' + scriptId,
    method: 'delete'
  })
}
