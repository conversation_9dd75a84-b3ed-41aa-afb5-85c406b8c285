import request from '@/utils/request'

// 查询剧本被授权店家列表
export function listScriptAuth(query) {
  return request({
    url: '/drama/scriptAuth/list',
    method: 'get',
    params: query
  })
}

// 查询剧本被授权店家详细
export function getScriptAuth(scriptId) {
  return request({
    url: '/drama/scriptAuth/' + scriptId,
    method: 'get'
  })
}

// 新增剧本被授权店家
export function addScriptAuth(data) {
  return request({
    url: '/drama/scriptAuth',
    method: 'post',
    data: data
  })
}

// 修改剧本被授权店家
export function updateScriptAuth(data) {
  return request({
    url: '/drama/scriptAuth',
    method: 'put',
    data: data
  })
}

// 删除剧本被授权店家
export function delScriptAuth(scriptId) {
  return request({
    url: '/drama/scriptAuth/' + scriptId,
    method: 'delete'
  })
}
