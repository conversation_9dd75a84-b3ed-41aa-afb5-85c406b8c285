import request from '@/utils/request'

// 查询店家列表
export function listStore(query) {
  return request({
    url: '/drama/store/list',
    method: 'get',
    params: query
  })
}

// 查询店家详细
export function getStore(storeId) {
  return request({
    url: '/drama/store/' + storeId,
    method: 'get'
  })
}

// 新增店家
export function addStore(data) {
  return request({
    url: '/drama/store',
    method: 'post',
    data: data
  })
}

// 修改店家
export function updateStore(data) {
  return request({
    url: '/drama/store',
    method: 'put',
    data: data
  })
}

// 删除店家
export function delStore(storeId) {
  return request({
    url: '/drama/store/' + storeId,
    method: 'delete'
  })
}
