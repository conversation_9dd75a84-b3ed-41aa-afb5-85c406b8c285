<!-- @/components/jbs/BottomReplyInput.vue -->
<script setup lang="ts">
import { ref, watch } from 'vue'

// Props
const props = defineProps<{
    modelValue: boolean // 控制弹窗显示（v-model）
    placeholder?: string // 输入框提示文字
    toName?: string // 被回复的用户名（可选）
}>()

// Emits
const emit = defineEmits<{
    (e: 'update:modelValue', value: boolean): void
    (e: 'submit', content: string): void
}>()

// 输入内容
const inputValue = ref('')

// 监听 modelValue 变化，关闭时清空输入
watch(
    () => props.modelValue,
    (newVal) => {
        if (!newVal) {
            inputValue.value = ''
        }
    }
)

// 提交评论
const handleSubmit = () => {
    const content = inputValue.value.trim()
    if (!content) return
    emit('submit', content)
    // 提交后不清空，由父组件控制关闭后再清空
}

// 关闭弹窗
const close = () => {
    emit('update:modelValue', false)
}
</script>

<template>
    <!-- 遮罩层 -->
    <view v-if="modelValue" class="overlay" @click="close" />

    <!-- 底部弹出层 -->
    <view v-if="modelValue" class="input-popup">
        <!-- 输入区域 -->
        <view class="input-wrapper">
            <textarea v-model="inputValue" class="input-field"
                :placeholder="placeholder || (toName ? `回复 @${toName}` : '说点什么...')" auto-focus fixed></textarea>
        </view>

        <!-- 操作按钮 -->
        <view class="action-buttons">
            <text class="cancel-btn" @click="close">取消</text>
            <text class="send-btn" :class="{ 'send-btn-disabled': !inputValue.trim() }" @click="handleSubmit">
                发送
            </text>
        </view>
    </view>
</template>

<style scoped lang="scss">
.overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background-color: rgba(0, 0, 0, 0.4);
    z-index: 999;
}

.input-popup {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    background-color: #fff;
    z-index: 1000;
    padding: 20rpx 30rpx;
    box-shadow: 0 -2rpx 20rpx rgba(0, 0, 0, 0.1);
    transform: translateY(0);
    animation: slideUp 0.3s ease-out;

    @keyframes slideUp {
        from {
            transform: translateY(100%);
        }

        to {
            transform: translateY(0);
        }
    }
}

.input-wrapper {
    border: 1px solid #ddd;
    border-radius: 10rpx;
    padding: 20rpx;
    margin-bottom: 20rpx;
}

.input-field {
    width: 100%;
    font-size: 28rpx;
    color: #333;
    line-height: 1.5;
    height: 150rpx;
    padding: 0;
}

.action-buttons {
    display: flex;
    justify-content: flex-end;
    gap: 40rpx;
    font-size: 30rpx;
}

.cancel-btn {
    color: #999;
}

.send-btn {
    color: #007aff;
    font-weight: 500;
}

.send-btn-disabled {
    color: #ccc;
    opacity: 0.6;
}
</style>