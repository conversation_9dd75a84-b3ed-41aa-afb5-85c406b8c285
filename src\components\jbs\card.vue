<script setup lang="ts">
import { defineProps } from 'vue';
import type { Script } from '@/types/Script';
enum CardDisplayMode {
    HOME = 'HOME',
    MYFEATURES = 'MYFEATURES',
    WANTPALY = 'WANTPALY',
    DYNAMIC = 'DYNAMIC',
    ListTopics = 'ListTopics'
}
const paymentStatusMap: Record<number, string> = {
    0: '未支付',
    1: '已支付',
    2: '去评价'
}
const wantStatusMap: Record<number, string> = {
    0: '想玩',
    1: '已想玩'
}
withDefaults(defineProps<{
    script: Script;
    displayMode?: `${CardDisplayMode}`;
}>(), {
    displayMode: CardDisplayMode.HOME
});

const goToPool = (id: number) => {
  uni.navigateTo({
    url: `/pages/shop/goPool/index?id=${id}`
  });
};
</script>
<template>
    <view class="card-box" :class="displayMode">
        <img :src="script.cover" class="script-kill-img" :class="displayMode" />
        <view class="card-right">
            <view class="title-rating-container">
                <view class="script-kill-title">{{ script.title }}</view>
                <view class="script-kill-score" v-if="displayMode === CardDisplayMode.MYFEATURES">
                    {{ script.score }} 分
                </view>
                <view class="script-kill-pooling" v-if="displayMode === CardDisplayMode.MYFEATURES">
                    <view class="pooling-item">拼场中</view>
                    <view class="pooling-already-number">{{ script.alreadyNumber }}</view>
                    <view class="pooling-total-number">/{{ script.totalNumber }}</view>
                </view>
                <view class="want-play-status"
                    v-if="displayMode === CardDisplayMode.WANTPALY || displayMode === CardDisplayMode.ListTopics">
                    <view class="want-play">
                        <up-icon name="heart-fill" color="#409EFF" size="18" v-if="script.wantPlayStatus === 1" />
                        <up-icon name="heart" color="#e8f2ff" size="18" v-else />
                        <text class="want-play-text">{{ wantStatusMap[script.wantPlayStatus] }}</text>
                    </view>
                </view>
            </view>
            <view class="up-tag-price">
                <view>
                    <up-tag v-for="(item, index) in script.tags" :text="item" :key="index" plain plainFill
                        borderColor="transparent" class="tag-item" size="mini" color="#867676"
                        v-if="displayMode === CardDisplayMode.MYFEATURES || displayMode === CardDisplayMode.HOME || displayMode === CardDisplayMode.WANTPALY" />
                    <text class="tag-text"
                        v-if="displayMode === CardDisplayMode.DYNAMIC || displayMode === CardDisplayMode.ListTopics">{{
                            script.tags.join(' | ')
                        }}</text>
                </view>
                <view class="script-kill-price" v-if="displayMode === CardDisplayMode.MYFEATURES">
                    ¥{{ script.price }}
                </view>
            </view>
            <view class="text-payment-status">
                <view class="script-kill-text">
                    {{ script.players }} · {{ script.duration }} · {{ script.difficulty }}
                </view>
                <view class="payment-status" v-if="displayMode === CardDisplayMode.MYFEATURES">
                    <text v-if="script.paymentStatus !== 2">
                        {{ paymentStatusMap[script.paymentStatus] || '未知状态' }}</text>
                    <up-button v-else-if="script.paymentStatus === 2" type="primary" text="去评价"
                        class="payment-button" />
                </view>
            </view>
            <view class="address-distance" v-if="displayMode === CardDisplayMode.HOME">
                <text class="script-kill-address">{{ script.address }}</text>
                <text class="script-kill-address"> · </text>
                <text class="script-kill-distance">{{ script.distance }}</text>
            </view>
            <view class="avatar-user"
                v-if="displayMode === CardDisplayMode.HOME || displayMode === CardDisplayMode.MYFEATURES">
                <up-avatar-group :urls="script.avatars" size="24" gap="0.4" class="script-kill-avatar" />
                <view class="time-text">
                    <text v-if="displayMode === CardDisplayMode.HOME">
                        等<text>{{ script.waitingNumber }}</text>人·
                    </text>
                    <text>{{ script.startTime }}</text>
                </view>
            </view>
            <view class="brief-introduction" v-if="displayMode === CardDisplayMode.WANTPALY">
                <up-icon name="order" color="#409EFF" size="18"></up-icon>
                <text class="introduction-text">{{ script.introduction }}</text>
            </view>
            <view class="heat-button" v-if="displayMode === CardDisplayMode.HOME">
                <view class="action-heat">
                    <view class="script-kill-heat">热度:</view>
                    <up-rate :count="5" v-model="script.heat" class="script-kill-rate" :gutter="2" :size="10"
                        :activeColor="'#ffa500'" readonly />
                </view>
                <up-button type="primary" text="去拼场" class="script-kill-button" @click="goToPool(script.id)" />
            </view>
        </view>
    </view>
</template>
<style scoped lang="scss">
@import "@/static/scss/variables.module.scss";

%display-center {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-box {
    padding: $sp-page;
    background-color: white;
    $main-font-color: $primary-text-color;
    $main-margin-top: 8rpx;
    display: flex;
    justify-content: space-between;
    gap: 30rpx;

    &.HOME {
        height: 380rpx;
    }

    &.MYFEATURES,
    &.WANTPALY {
        height: 286rpx;
    }

    &.DYNAMIC {
        height: 143rpx;
    }

    &.ListTopics {
        height: 210rpx;
    }


    .script-kill-img {
        border-radius: $border-radius-small;

        &.HOME {
            flex: 4;
            width: 195rpx;
        }

        &.MYFEATURES {
            flex: 2.5;
            width: 155rpx;
        }
    }


    .card-right {
        flex: 8;
        text-align: left;
        display: flex;
        flex-direction: column;
        justify-content: space-between;

        .title-rating-container {
            @extend %display-center;
            align-items: end;
            gap: 10rpx;

            .script-kill-title {
                font-size: 32rpx;
                color: $main-font-color;
                font-weight: bold;
                display: inline-block;
            }

            .script-kill-score {
                font-size: 22rpx;
                align-self: flex-end;
                color: #ff0000;
            }

            .script-kill-pooling {
                display: flex;
                align-items: baseline;
                margin-left: auto;

                .pooling-item {
                    font-size: 26rpx;
                }

                .pooling-already-number {
                    font-size: 22rpx;
                    color: #409EFF;
                }

                .pooling-total-number {
                    font-size: 22rpx;
                }
            }

            .want-play-status {
                .want-play {
                    display: flex;

                    .want-play-text {
                        font-size: 26rpx;
                        color: #3d6cfc;
                    }
                }
            }
        }

        .up-tag-price {
            @extend %display-center;

            .tag-text {
                font-size: 23rpx;
                color: #867676;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }

            .tag-item {
                margin-top: $main-margin-top;
                margin-right: $sp-small;
                color: $main-font-color;
            }

            .script-kill-price {
                margin-left: auto;
                font-weight: 600;
            }
        }

        .text-payment-status {
            @extend %display-center;

            .script-kill-text {
                font-size: 26rpx;
                color: $main-font-color;
            }

            .payment-status {
                color: #00b3ff;
                margin-left: auto;

                .payment-button {
                    height: 50rpx;
                }
            }
        }

        .address-distance {
            @extend %display-center;

            text {
                display: block;
            }

            .script-kill-address {
                color: $secondary-text-color;
                font-size: 22rpx;
            }

            .script-kill-distance {
                color: $main-font-color;
                font-size: 24rpx;
            }
        }

        .avatar-user {
            @extend %display-center;
            justify-content: start;

            .time-text {
                margin-left: 8rpx;
                font-size: 24rpx;
                color: $main-font-color;
                white-space: nowrap;
            }
        }

        .brief-introduction {
            @extend %display-center;
            background: #f2faff;
            border-radius: 4rpx;

            .introduction-text {
                display: -webkit-box;
                line-clamp: 1;
                -webkit-line-clamp: 1;
                -webkit-box-orient: vertical;
                font-size: 20rpx;
                overflow: hidden;
                text-overflow: ellipsis;
            }
        }

        .heat-button {
            @extend %display-center;

            .action-heat {
                display: flex;
                padding: 2rpx 20rpx;
                background-color: #fff9db;
                border: 1px solid #fff9db;
                border-radius: 80rpx;


                .script-kill-heat {
                    font-size: 16rpx;
                    margin-right: 10rpx;
                }
            }

            .script-kill-button {
                width: 140rpx;
                height: 50rpx;
                margin: 0;
            }
        }

    }
}
</style>
