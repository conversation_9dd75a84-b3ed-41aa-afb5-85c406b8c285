<!-- @/components/jbs/commentItem.vue -->
<script setup lang="ts">
import { defineProps, ref } from 'vue'

interface Comment {
    id: string
    userId: string
    userName: string
    userAvatar: string
    content: string
    date: string
    location: string
    replies: Comment[]
    myLiked?: boolean  // 
}

// Props
const props = defineProps<{
    comment: Comment
}>()

const emit = defineEmits<{
    (e: 'update:isLiked', value: boolean): void
    (e: 'openReplyInput', targetId: string): void
    (e: 'submitReply', replyData: { targetId: string; content: string; newReply: Comment }): void
    // 新增：通知父组件某条回复被点赞了
    (e: 'like-reply', replyId: string, liked: boolean): void

}>()

const handleLike = () => {
    // 获取当前 myLiked 状态，取反
    const newLiked = !props.comment.myLiked

    emit('update:isLiked', newLiked)
}
const handleReplyLike = (reply: Comment) => {
    const newValue = !reply.myLiked
    emit('like-reply', reply.id, newValue)
}
const openReplyInput = () => {
    emit('openReplyInput', props.comment.id)
}

const replyToReply = (replyId: string) => {
    emit('openReplyInput', replyId)
}
// 是否展开更多回复
const showMoreReplies = ref(false)
// 切换展开状态
const toggleShowReplies = () => {
    showMoreReplies.value = !showMoreReplies.value
}
</script>

<template>
    <view class="comment-item">
        <!-- 用户信息 -->
        <image :src="comment.userAvatar" class="avatar" mode="aspectFill" />
        <view class="right">
            <view class="user-info">
                <view class="user-details">
                    <text class="username">{{ comment.userName }}</text>
                    <text v-if="comment.userId === 'author'" class="author">作者</text>
                </view>
            </view>

            <!-- 评论内容 -->
            <view class="content">
                {{ comment.content }}
            </view>

            <!-- 评论底部 -->
            <view class="comment-bottom">
                <text class="date-location">{{ comment.date }} {{ comment.location }}</text>
                <view class="actions">
                    <view class="action-item" @click="openReplyInput">
                        <text class="action-text">回复</text>
                    </view>
                    <view class="action-item" @click="handleLike">
                        <up-icon :name="props.comment.myLiked ? 'thumb-up-fill' : 'thumb-up'"
                            :color="props.comment.myLiked ? '#ff0000' : ''" size="30" />
                    </view>
                </view>
            </view>

            <!-- 二级回复区域 -->
            <view v-if="comment.replies.length > 0" class="replies-list">
                <!-- 始终显示第一条 -->
                <image :src="comment.replies[0].userAvatar" class="avatar" mode="aspectFill" />
                <view class="right">
                    <view class="reply-item">

                        <view class="user-info">

                            <view class="user-details">
                                <text class="username">{{ comment.replies[0].userName }}</text>
                                <text class="reply-to">回复 {{ comment.userName }}:</text>
                            </view>
                        </view>
                        <view class="content">
                            {{ comment.replies[0].content }}
                        </view>
                        <view class="reply-bottom">
                            <text class="date-location">{{ comment.replies[0].date }} {{ comment.replies[0].location
                            }}</text>
                            <view class="actions">
                                <view class="action-item" @click="replyToReply(comment.replies[0].id)">
                                    <text class="action-text">回复</text>
                                </view>
                                <!-- 改为：带状态和点击 -->
                                <view class="action-item" @click="handleReplyLike(comment.replies[0])">
                                    <up-icon :name="comment.replies[0].myLiked ? 'thumb-up-fill' : 'thumb-up'"
                                        :color="comment.replies[0].myLiked ? '#ff0000' : ''" size="30" />
                                </view>
                            </view>
                        </view>
                    </view>

                    <!-- 展开其余回复 -->
                    <template v-if="comment.replies.length > 1">
                        <!-- 展开按钮（在第一条之后） -->
                        <view v-if="!showMoreReplies" class="expand-btn" @click="toggleShowReplies">
                            展开 {{ comment.replies.length - 1 }} 条回复
                            <up-icon name="arrow-down" size="24" color="#999" />
                        </view>

                        <!-- 显示其余回复 -->
                        <view v-if="showMoreReplies" class="more-replies">
                            <view v-for="reply in comment.replies.slice(1)" :key="reply.id" class="reply-item">
                                <view class="user-info">
                                    <image :src="reply.userAvatar" class="avatar" mode="aspectFill" />
                                    <view class="user-details">
                                        <text class="username">{{ reply.userName }}</text>
                                        <text class="reply-to">回复 {{ comment.userName }}:</text>
                                    </view>
                                </view>
                                <view class="content">
                                    {{ reply.content }}
                                </view>
                                <view class="reply-bottom">
                                    <text class="date-location">{{ reply.date }} {{ reply.location }}</text>
                                    <view class="actions">
                                        <view class="action-item" @click="replyToReply(reply.id)">
                                            <text class="action-text">回复</text>
                                        </view>
                                        <view class="action-item" @click="handleReplyLike(reply)">
                                            <up-icon :name="reply.myLiked ? 'thumb-up-fill' : 'thumb-up'"
                                                :color="reply.myLiked ? '#ff0000' : ''" size="30" />
                                        </view>
                                    </view>
                                </view>
                            </view>

                            <!-- 收起按钮：显示在最后一条回复之后 -->
                            <view class="expand-btn" @click="toggleShowReplies">
                                收起
                                <up-icon name="arrow-up" size="24" color="#999" />
                            </view>
                        </view>
                    </template>
                </view>
            </view>
        </view>
    </view>
</template>

<style scoped lang="scss">
.comment-item {
    padding: 20rpx 0;
    position: relative;
    justify-content: space-between;
    display: flex;
    gap: 10rpx;

    &:last-child {
        border-bottom: none;
    }

    .avatar {
        width: 60rpx;
        height: 60rpx;
        border-radius: 50%;

    }

    .right {
        flex: 1;
    }

    .user-info {
        display: flex;
        align-items: center;
        margin-bottom: 10rpx;



        .user-details {
            display: flex;
            align-items: center;

            .username {
                font-size: 28rpx;
                color: #333;
                font-weight: 500;
            }

            .author {
                font-size: 20rpx;
                color: #007bff;
                background-color: #eef1f5;
                padding: 2rpx 8rpx;
                border-radius: 6rpx;
                margin-left: 8rpx;
            }
        }
    }

    .content {
        font-size: 28rpx;
        color: #333;
        line-height: 1.6;
        margin-bottom: 12rpx;
    }

    .comment-bottom {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 22rpx;
        color: #999;
        gap: 20rpx;

        .actions {
            display: flex;
            align-items: center;
            gap: 20rpx;

            .action-item {
                display: flex;
                align-items: center;
                gap: 6rpx;
                cursor: pointer;
            }

            .action-text {
                font-size: 22rpx;
            }
        }
    }

    .replies-list {
        margin: 5rpx;
        justify-content: space-between;
        display: flex;
        gap: 10rpx;

        .reply-item {
            margin-bottom: 10rpx;

            .user-info {
                margin-bottom: 5rpx;

                .reply-to {
                    font-size: 24rpx;
                    color: #666;
                    margin-left: 8rpx;
                }
            }

            .content {
                font-size: 26rpx;
                color: #333;
                line-height: 1.5;
                margin-bottom: 8rpx;
            }

            .reply-bottom {
                display: flex;
                justify-content: space-between;
                align-items: center;
                font-size: 22rpx;
                color: #999;
                gap: 20rpx;

                .actions {
                    display: flex;
                    align-items: center;
                    gap: 20rpx;
                }
            }
        }

        .expand-btn {
            display: flex;
            align-items: center;
            gap: 8rpx;
            font-size: 24rpx;
            color: #007aff;
            margin: 10rpx 0;
            cursor: pointer;
        }
    }
}
</style>