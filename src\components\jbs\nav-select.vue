<script lang="ts" setup>
import { onMounted } from 'vue';

const props = withDefaults(defineProps<{
    nav: string[]
    flex: string
}>(), {
    flex: 'start'
});
const model = defineModel({
    type: String,
    default: () => ''
})
onMounted(() => {
    if (!props.nav || props.nav.length === 0) {
        console.warn('NavSelect: nav prop is required and cannot be empty');
        return;
    }
    if (model.value === '') {
        model.value = props.nav[0];
    }
})

</script>
<template>
    <view class="filter-container" :style="{ 'justify-content': props.flex }">
        <view v-for="item in props.nav" :key="item" class="filter-item" :class="{ 'is-active': model === item }"
            @click="model = item">
            <text>{{ item }}</text>
        </view>
    </view>
</template>
<style scoped lang="scss">
@import "@/static/scss/leyout.scss";

.filter-container {
    display: flex;
    align-items: end;
    gap: $sp-page;
    padding: $sp-page 0;
    height: calc(48rpx + 2 * $sp-page);


    .filter-item {
        text {
            font-size: 28rpx;
            transition: font-size 0.3s ease;
        }


        &.is-active {
            position: relative;


            text {
                font-size: 36rpx;
                font-weight: 600;
                z-index: 2;
                position: relative;
            }

            &::before {
                @extend .small-block;
                content: '';
                position: absolute;
                bottom: -8rpx;
                left: calc(50% - 30rpx);
                z-index: 1;
            }
        }
    }
}
</style>