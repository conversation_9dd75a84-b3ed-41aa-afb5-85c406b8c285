<script lang="ts" setup>
import { onMounted, ref } from "vue";
import { getAddress, openThirdPartyMap, MAP_TYPES, openWeixinMap ,getCoordinatesByAddress} from "@/utils/location";
import positioning from "@/static/images/icon/positioning.png";
import modal from "@/plugins/modal";


/**位置相关的数据 */
const currentLocation = ref("");
const loading = ref(false);

/**指定地图类型( auto表示三种都可以打开)  */
const mapType = ref(MAP_TYPES.AUTO);

// 获取当前位置
const handleGetLocation = async () => {
    if (loading.value) return;
    loading.value = true;
    try {
        const res = await getAddress();
        currentLocation.value = res.address.poiName;
    } catch (error) {
        currentLocation.value = "请定位";
    } finally {
        loading.value = false;
    }
};

// 点击导航到当前位置
const handleNavigateToLocation = () => {
    if (!currentLocation.value) {
        modal.msg('请先获取位置')
        return;
    }
    /**测试数据 */
    const options = {
        latitude: 39.908823,//纬度
        longitude: 116.397470,//经度
        name: '天安门广场', //位置名
        address: '北京市东城区正义路1号'//地址的详细说明
    };

    // #ifdef MP-WEIXIN
    openWeixinMap(options);
    // #endif

    // #ifndef MP-WEIXIN
    openThirdPartyMap(options, mapType.value);
    // #endif


};


onMounted(() => {
    handleGetLocation(); // 页面加载时获取位置
});

</script>
<template>
    <view class="positioning-container">
        <image :src="positioning" mode="" @tap="handleGetLocation" :class="{ 'loading': loading }" />
        <text @tap="handleNavigateToLocation">{{ currentLocation }}</text>
    </view>
</template>
<style scoped lang="scss">
@import "@/static/scss/leyout.scss";

.positioning-container {
    $font-size: 32rpx;
    display: flex;
    align-items: center;
    justify-content: start;
    padding: 0 $sp-page;

    image {
        height: $font-size;
        width: $font-size;

        &.loading {
            opacity: 0.6;
        }
    }

    text {
        display: block;
        font-size: $font-size;
        font-weight: 500;
    }
}
</style>