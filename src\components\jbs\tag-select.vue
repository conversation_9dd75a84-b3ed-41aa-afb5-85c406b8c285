<script setup lang="ts">
import { onMounted, reactive, ref } from 'vue';
const selected = defineModel({
    type: Object,
    default: () => ({})
});
interface Tag {
    name: string;
    label: string;
    value: any;
    children?: { label: string; value: any }[];
    childrenVisible?: boolean;
}
const tags = reactive([
    {
        name: 'nearby', label: '附近', value: 'all', children: [
            { label: '全部', value: 'all' },
            { label: '附近', value: '1km' },
            { label: '3公里', value: '3km' },
            { label: '5公里', value: '5km' },
            { label: '10公里', value: '10km' },
        ]
    },
    { name: 'type', label: '类型', value: false },
    { name: 'hot', label: '开场', value: true },
    {
        name: 'time', label: '时间段', value: false, children: [
            { label: '今天', value: 'today' },
            { label: '明天', value: 'tomorrow' },
            { label: '周末', value: 'weekend' },
            { label: '下周', value: 'nextWeek' },
            { label: '时间段', value: false },
        ]
    },
    { name: 'classic', label: '随机免单拼车', value: false },
    { name: 'mystery1', label: '急速拼车', value: false },
    { name: 'mystery2', label: '百亿补贴', value: false },
    { name: 'mystery3', label: '多车拼车', value: false },

]);
type TagSelectColumn = { label: string; value: any };
const columns = ref<TagSelectColumn[][]>([]);
let getPickerDataCallback = (value: TagSelectColumn) => { };
const getPickerData = () => {
    return new Promise<TagSelectColumn>((resolve) => {
        getPickerDataCallback = resolve;
    });
};
const toggleChildren = (tag: Tag) => {
    if (tag.children) {
        columns.value = [tag.children];
        show.value = true;
        getPickerData().then(value => {
            tag.value = value.value;
            tag.label = value.label;
        });
    } else {
        tag.value = !tag.value;
    }
    selected.value[tag.name] = tag.value;
};
onMounted(() => {
    // 初始化selected对象
    tags.forEach(tag => {
        selected.value[tag.name] = tag.value;
    });
});

const show = ref(false);
function confirm({ value }: { value: TagSelectColumn[] }) {
    getPickerDataCallback(value[0])
    show.value = false;
}
</script>
<template>
    <view class="tag-select">
        <view class="tag-item" v-for="(tag, index) in tags" :key="index" @tap="toggleChildren(tag)"
            :class="{ 'is-active': !!tag.value }">
            <text>{{ tag.label }}</text>
            <up-icon v-show="!!tag.children" name="arrow-down-fill" size="12" />
        </view>
        <up-picker :show="show" :columns="columns" keyName="label" valueName="value" @confirm="confirm"
            @cancel="show = false"></up-picker>
    </view>
</template>
<style scoped lang="scss">
@import "@/static/scss/leyout.scss";

.tag-select {
    display: flex;
    overflow-x: auto;
}

.tag-item {
    padding: 8rpx 12rpx;
    border-radius: $border-radius-small;
    background-color: transparentize($auxiliary-color-gray, 0.5);
    font-size: 24rpx;
    display: flex;
    align-items: center;

    &.is-active {
        background-color: $auxiliary-color-gray;
    }

    &+.tag-item {
        margin-left: 8rpx;
    }

    text,
    .u-icon {
        display: block;
        white-space: nowrap;
    }
}
</style>