<template>
  <view class="map-container">
    <!-- #ifdef MP-WEIXIN -->
    <map
      :latitude="latitude"
      :longitude="longitude"
      :scale="scale"
      :markers="markers"
      :show-location="true"
      style="width: 100%; height: 300px;"
      @markertap="onMarkerTap"
      @tap="onMapTap"
    />
    <!-- #endif -->
    
    <!-- #ifndef MP-WEIXIN -->
    <view class="map-placeholder">
      <text>当前平台不支持地图组件</text>
    </view>
    <!-- #endif -->
  </view>
</template>

<script lang="ts" setup>
import { ref } from 'vue';

interface Props {
  latitude?: number;
  longitude?: number;
  scale?: number;
}

const props = withDefaults(defineProps<Props>(), {
  latitude: 39.908823,
  longitude: 116.397470,
  scale: 16
});

const markers = ref([
  {
    id: 1,
    latitude: props.latitude,
    longitude: props.longitude,
    title: '目标位置',
    iconPath: '/static/images/marker.png',
    width: 30,
    height: 30
  }
]);

const onMarkerTap = (e: any) => {
  console.log('点击标记:', e);
  // 打开导航
  uni.openLocation({
    latitude: props.latitude,
    longitude: props.longitude,
    name: '目标位置'
  });
};

const onMapTap = (e: any) => {
  console.log('点击地图:', e);
};
</script>

<style scoped>
.map-container {
  width: 100%;
  height: 300px;
}

.map-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 300px;
  background-color: #f5f5f5;
}
</style>