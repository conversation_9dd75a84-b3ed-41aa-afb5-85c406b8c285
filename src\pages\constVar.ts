import { Script } from "@/types/Script";
import xuexiang from "@/static/images/cover/xuexiang.jpg";


export function getData(): Promise<Script[]> {
    return new Promise<Script[]>((resolve) => {
        const scriptList: Script[] = [];
        for (let index = 0; index < 30; index++) {
            scriptList.push({
                id: 5,
                title: '雪乡连环杀人事件',
                cover: xuexiang,
                tags: ['推理', '惊悚', '现代', '本格'],
                players: '3男3女',
                duration: '5小时',
                difficulty: '适中',
                address: '山西省太原市茂业一期光影剧本杀',
                distance: '10.0km',
                avatars: [
                    'https://uview-plus.jiangruyi.com/uview-plus/album/1.jpg',
                    'https://uview-plus.jiangruyi.com/uview-plus/album/2.jpg',
                    'https://uview-plus.jiangruyi.com/uview-plus/album/3.jpg',
                    'https://uview-plus.jiangruyi.com/uview-plus/album/4.jpg',
                    'https://uview-plus.jiangruyi.com/uview-plus/album/7.jpg',
                    'https://uview-plus.jiangruyi.com/uview-plus/album/6.jpg',
                    'https://uview-plus.jiangruyi.com/uview-plus/album/5.jpg'
                ],
                waitingNumber: '2',
                startTime: '今天17:00',
                rating: 3,
                heat: 3,
                score: 9.2,
                alreadyNumber: 6,
                totalNumber: 8,
                price: 98,
                introduction: '年三十，北道河，村里出了个杀人魔。杀了一个又一个，最后一个杀老婆。七个小孩儿来串门，联起手来把案破。',
                paymentStatus: 2,
                wantPlayStatus: 1
            });
        }
        setTimeout(() => {
            resolve(scriptList);
        }, 1000);
    });
}