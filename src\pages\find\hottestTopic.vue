<script setup lang="ts">
import { defineProps, defineEmits } from 'vue';

interface Script {
    rank: number
    title: string
    description: string
    participants: number
    authorAvatar: string
    authorName: string
    comment: string
    hashtag: string
}

defineProps<{
    script: Script;
}>();
const emit = defineEmits<{
    (e: 'click'): void
}>();
</script>
<template>
    <up-card :border="false" padding="0" :show-foot="false" :border-radius="12" :head-border-bottom="false" margin="0">
        <template #body>
            <view class="card-body" @click="emit('click')">
                <!-- 左侧内容 -->
                <view class="left-content">
                    <view v-if="script.rank && script.rank <= 3" class="top-badge">
                        <view class="badge-bg"></view>
                        <span class="badge-text">{{ `TOP${script.rank}` }}</span>
                    </view>
                    <view class="title-container">
                        <view class="tag">#</view>
                        <view class="title" :class="script.title.length > 10 ? 'double-line' : 'single-line'">
                            {{ script.title }}
                        </view>
                    </view>
                    <view class="description" :class="script.title.length > 10 ? 'single-line' : 'double-line'"
                        v-if="script.description">
                        "{{ script.description }}"
                    </view>
                    <view class="participants" v-if="script.participants">
                        <span class="dot"></span>
                        <text>{{ script.participants }}人参与此话题</text>
                    </view>
                </view>

                <!-- 右侧内容 -->
                <view class="right-content">
                    <view class="avatar-and-name">
                        <image :src="script.authorAvatar" class="avatar"></image>
                        <text class="name">{{ script.authorName }}</text>
                    </view>
                    <view class="comment" v-if="script.comment">
                        {{ script.comment }}
                    </view>
                    <view class="parent" v-if="script.hashtag">
                        <view class="hashtags">
                            <text class="hash">#</text>{{ script.hashtag }}
                            <text class="hash">#</text>
                        </view>
                    </view>
                </view>
            </view>
        </template>
    </up-card>
</template>
<style scoped lang="scss">
@import "@/static/scss/leyout.scss";

/* 样式部分保持不变 */
.card-body {
    @extend .flex-rsb;
    padding: $sp-card;
    position: relative;
    gap: 15rpx;
}

.left-content {
    width: 60%;
    padding-top: 10rpx;
    display: flex;
    flex-direction: column;
    gap: 8rpx;

    .top-badge {
        position: absolute;
        top: 0;
        left: 0;
        font-size: 20rpx;
        color: #fff;
        padding: 0.18rem 0.1975rem;
        border-radius: 12rpx 0rpx 12rpx 0rpx;
        overflow: hidden;
        z-index: 1;

        &::before {
            content: '';
            position: absolute;
            top: 0;
            left: -10rpx;
            width: 0;
            height: 0;
            border-top: 10rpx solid transparent;
            border-right: 10rpx solid #FFA500;
            border-bottom: 10rpx solid transparent;
        }

        .badge-bg {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, rgba($auxiliary-color-orange, 0.26), rgba($auxiliary-color-red, 0.16));
            z-index: 0;
        }

        .badge-text {
            position: relative;
            z-index: 1;
            font-weight: bold;
            color: white;
            background: linear-gradient(90deg, rgba($auxiliary-color-orange, 1), rgba($auxiliary-color-red, 0.9));
            /* 文字渐变颜色 */
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
        }

    }
}


.title-container {
    display: flex;
    align-items: flex-start;
    margin-top: 8rpx;

    .tag {
        @extend .flex-center;
        color: white;
        font-size: 28rpx;
        background-color: rgba($main-color, 0.75);
        border-radius: 50%;
        width: 32rpx;
        height: 32rpx;
        margin: 5rpx 5rpx 8rpx 0;
        min-height: 32rpx;
        min-width: 32rpx;
    }
}

.title {
    font-weight: bold;
    font-size: 30rpx;
    color: #333;
    line-height: 1.4;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;

    &.single-line {
        line-clamp: 1;
        -webkit-line-clamp: 1;
    }

    &.double-line {
        line-clamp: 2;
        -webkit-line-clamp: 2;
    }

}

.description {
    font-size: 24rpx;
    color: #666;
    line-height: 1.5;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;

    &.single-line {
        line-clamp: 1;
        -webkit-line-clamp: 1;
    }

    &.double-line {
        line-clamp: 2;
        -webkit-line-clamp: 2;
    }
}

.participants {
    font-weight: bold;
    font-size: 20rpx;
    color: #333;
    flex-shrink: 0;
    display: flex;
    flex-direction: row;
    gap: 8rpx;
    align-items: center;

    .dot {
        display: inline-block;
        width: 6rpx;
        height: 6rpx;
        background-color: rgba($main-color, 0.6);
        border-radius: 50%;
    }
}

.right-content {
    width: 45%;
    background-color: rgba($main-color, 0.07);
    border-radius: 8rpx;
    padding: 10rpx;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
    gap: $sp-small;

    .avatar-and-name {
        display: flex;
        align-items: center;
        flex-direction: row;
        gap: 12rpx;

        .name {
            font-size: 24rpx;
            font-weight: bold;
            color: #333;
        }

        .avatar {
            width: 40rpx;
            height: 40rpx;
            border-radius: 50%;
        }
    }

    .comment {
        font-size: 20rpx;
        color: #666;
        line-height: 1.4;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        line-clamp: 2;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
    }


    .parent {
        display: flex;
        flex-direction: row;
        justify-content: flex-end;
        align-items: center;
        flex-wrap: nowrap;

        .hashtags {
            display: inline-flex;
            flex-direction: row;
            align-items: center;
            flex-wrap: nowrap;
            background-color: rgba($auxiliary-color-orange, 0.14);
            color: rgba($auxiliary-color-orange, 1);
            font-size: 22rpx;
            line-height: 30rpx;
            /* 与字体大小保持一致 */
            border-radius: 4rpx;
            font-weight: bold;
            white-space: nowrap;

            .hash {
                font-weight: bold;
                font-size: inherit;
                padding: 0 6rpx;
                line-height: 30rpx;
            }
        }
    }
}
</style>
