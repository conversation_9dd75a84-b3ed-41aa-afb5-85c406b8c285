<script setup lang="ts">
import Positioning from '@/components/jbs/positioning.vue';
import NavSelect from '@/components/jbs/nav-select.vue';
import waterfall from '@/pages/home/<USER>';
import { ref, reactive, computed } from 'vue';
import HottestTopic from './hottestTopic.vue'
const keyword = ref('');
import profile from '@/static/images/profile.jpg'
const scriptList = reactive([
    {
        rank: 1,
        title: '剧本杀初体验因为那时候对剧本杀不是很了解这是我玩的',
        description: '这是我玩的第一个本，因为那时候对剧本杀不是很了解这是我玩的第一个本，因为那时候对剧本杀不是很了解',
        participants: 65,
        authorAvatar: profile,
        authorName: '最热帖的主人',
        comment: '剧本非常好，逻辑清晰，没有漏洞啊哈，哈哈哈剧本非常这是我玩的第一个本，因为那时候对剧本杀不是很了解',
        hashtag: '剧本杀初体验',
        marginBottom: 20
    }
]);
const currentScript = scriptList[0];
const goToTopic = (scriptId: number) => {
  uni.navigateTo({
    url: `/pages/find/topic/index?id=${scriptId}`
  });
};
const nav = ref("推荐")
const navs = ref(['推荐', '最新', '最热话题']);
const current = computed({
    get: () => {
        return navs.value.indexOf(nav.value);
    },
    set: (value) => {
        nav.value = navs.value[value];
    }
});
</script>
<template>
    <view class="app-container">
        <view class="top-container">
            <view class="top-search ppd-t">
                <Positioning />
                <up-search placeholder="搜索" v-model="keyword" shape="square" :showAction="false" />
            </view>
            <NavSelect class="ppd-l" :nav="navs" v-model="nav"  flex="start" />
        </view>
        <swiper class="swiper" :duration="500" style="height: 85vh;" :current="current"
            @change="current = $event.detail.current">
            <swiper-item>
                <view class="content">
                    <!-- <view v-for="i in 100"> -->
                        <waterfall />
                    <!-- </view> -->
                </view>
            </swiper-item>
            <swiper-item>
                <view class="content">
                    <view v-for="i in 10">
                        <HottestTopic :script="{ ...currentScript, rank: i }" @click="goToTopic(i)" />
                    </view>
                </view>
            </swiper-item>
            <swiper-item>
                <view class="content">
                    <view v-for="i in 10">
                        <HottestTopic :script="{ ...currentScript, rank: i }" />
                    </view>
                </view>
            </swiper-item>
        </swiper>
    </view>
</template>
<style scoped lang="scss">
@import "@/static/scss/leyout.scss";

.top-container {
    position: sticky;
    top: 0;
    z-index: 999;
}

.top-search {
    display: flex;
    padding-right: 180rpx;
}

.content {
    height: 100%;
    padding: 0 $sp-page;
    display: flex;
    flex-direction: column;
    gap: $sp-card;
    overflow-y: auto;
}
</style>