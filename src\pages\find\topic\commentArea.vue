<script setup lang="ts">
import { ref, defineProps, defineEmits } from 'vue'
import CommentItem from '@/components/jbs/commentItem.vue'
import bottomReplyInput from '@/components/jbs/bottomReplyInput.vue'
// === 类型定义（保持不变）===
interface Comment {
    id: string
    userId: string
    userName: string
    userAvatar: string
    content: string
    date: string // 格式如 "08.24 15:35"
    location: string
    myLiked?: boolean // 是否已点赞（默认 false）
    replies: Comment[]
}



interface User {
    avatar: string
    name: string
    timestamp: string
}

// === Props ===
const props = defineProps<{
    comments: Comment[]
    commentCount: number
    user: User
}>()

// === Emits ===
const emit = defineEmits<{
    // 当用户点赞/取消点赞时，通知父组件
    (e: 'update:comment-like', commentId: string, liked: boolean): void
    // 当提交新回复时
    (e: 'submit-reply', replyData: {
        targetId: string
        content: string
        newReply: Comment
    }): void
}>()



// === 事件处理函数 ===

const updateCommentMyLiked = (comments: Comment[], id: string, liked: boolean) => {
    for (const comment of comments) {
        if (comment.id === id) {
            comment.myLiked = liked
            return true
        }
        if (updateCommentMyLiked(comment.replies, id, liked)) {
            return true
        }
    }
    return false
}

const handleToggleLike = (commentId: string, liked: boolean) => {
    // 更新 comments 中的实际 myLiked 字段
    const success = updateCommentMyLiked(props.comments, commentId, liked)
    if (success) {
        // 通知父组件（比如用于调用 API）
        emit('update:comment-like', commentId, liked)
    }
}
const handleReplyLike = (replyId: string, liked: boolean) => {
    // 更新二级评论（回复）的点赞状态
    for (let comment of props.comments) {
        if (updateCommentMyLiked(comment.replies, replyId, liked)) {
            return
        }
    }
}
// === 回复相关状态 ===
const showReplyInput = ref(false)
const replyTargetId = ref<string | null>(null) // 被回复的目标 ID（comment.id 或 reply.id）
const replyToName = ref<string | undefined>()
const inputPlaceholder = ref<string>('')

// 打开回复输入框
const handleOpenReply = (comment: Comment, targetId: string) => {
    const target = findTarget(comment, targetId)
    replyToName.value = target?.userName
    replyTargetId.value = targetId

    // 设置 placeholder
    inputPlaceholder.value = target?.userName ? `回复 @${target.userName}` : '说点什么...'

    showReplyInput.value = true
}

// 递归查找目标评论（comment 或 reply）
const findTarget = (comment: Comment, targetId: string): Comment | null => {
    if (comment.id === targetId) return comment
    for (const reply of comment.replies) {
        if (reply.id === targetId) return reply
    }
    return null
}

// 提交回复
const handleSubmitReply = (content: string) => {
    if (!replyTargetId.value) return

    const newReply = {
        id: 'new_' + Date.now(),
        userId: 'current_user',
        userName: '我',
        userAvatar: props.user.avatar, // 使用当前用户头像
        content,
        date: formatNowDate(), // 如 "08.24 15:35"
        location: '北京',
        replies: [],
        myLiked: false, // ✅ 新回复默认未点赞
    }

    // 通知父组件提交回复
    emit('submit-reply', {
        targetId: replyTargetId.value,
        content,
        newReply
    })

    // 关闭输入框
    showReplyInput.value = false
    replyTargetId.value = null
}

const isLiked = ref(false)
// 格式化当前时间为 "MM.DD HH:mm"
const formatNowDate = () => {
    const now = new Date()
    const mm = String(now.getMonth() + 1).padStart(2, '0')
    const dd = String(now.getDate()).padStart(2, '0')
    const hh = String(now.getHours()).padStart(2, '0')
    const min = String(now.getMinutes()).padStart(2, '0')
    return `${mm}.${dd} ${hh}:${min}`
}
</script>

<template>
    <view class="comment-header">
        <!-- 评论数量 -->
        <text class="comment-count">评论({{ commentCount }})</text>

        <!-- 用户输入区 -->
        <view class="topInfo"  @click="showReplyInput = true">
            <img :src="user.avatar" class="avatar" />
            <input placeholder="快来发表你的评价吧 ~" class="input-box" />
        </view>

        <!-- 评论列表 -->
        <view class="comments-list">
            <CommentItem v-for="comment in comments" :key="comment.id" :comment="comment"
                @update:isLiked="(val) => handleToggleLike(comment.id, val)"
                @like-reply="(replyId, liked) => handleReplyLike(replyId, liked)"
                @openReplyInput="handleOpenReply(comment, $event)" />

            <!-- 底部输入框 -->
            <bottomReplyInput v-model="showReplyInput" :placeholder="inputPlaceholder" :toName="replyToName"
                @submit="handleSubmitReply" />
        </view>
    </view>
</template>

<style scoped lang="scss">
@import "@/static/scss/variables.module.scss";

.comment-header {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 30rpx;
    gap: 20rpx;

    .comment-count {
        font-size: 32rpx;
        color: #333;
        font-weight: bold;
        align-self: flex-start;
    }

    .topInfo {
        display: flex;
        justify-content: space-between;
        align-self: flex-start;
        width: 100%;
        gap: 20rpx;
        align-items: center;

        .avatar {
            width: 85rpx;
            height: 85rpx;
            border-radius: 50%;
        }

        .input-box {
            flex: 1;
            height: 80rpx;
            background-color: $background-color;
            border-radius: 10rpx;
            padding: 0 20rpx;
            font-size: 28rpx;
            color: #666;
        }
    }

    .comments-list {
        width: 100%;
        display: flex;
        flex-direction: column;
        gap: 30rpx;
        margin-top: 20rpx;
    }
}
</style>