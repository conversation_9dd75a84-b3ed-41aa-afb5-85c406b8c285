<script setup lang="ts">
import { ref, reactive, computed } from 'vue';
import commentArea from './commentArea.vue';
import profile from '@/static/images/profile.jpg'
import xuexiang from "@/static/images/cover/xuexiang.jpg";
import type { Script } from '@/types/Script';
import topicContent from './topicContent.vue';
import bottomReplyInput from '@/components/jbs/bottomReplyInput.vue';
const showReplyInput = ref(false)
const handleCommentSubmit = (content: string) => {
    const newComment: Comment = {
        id: Date.now().toString(),
        userId: 'myId',
        userName: '啊哈哈哈',
        userAvatar: profile,
        content,
        date: '刚刚',
        location: '太原',
        myLiked: false,
        replies: []
    }
    showReplyInput.value = false
    comments.value.unshift(newComment)

}
const scriptList = reactive<Script[]>([
    {
        id: 7,
        title: '雪乡连环杀人事件',
        cover: xuexiang,
        tags: ['推理', '惊悚', '现代', '本格'],
        players: '3男3女',
        duration: '5小时',
        difficulty: '适中',
        address: '山西省太原市茂业一期光影剧本杀',
        distance: '10.0km',
        avatars: [
            'https://uview-plus.jiangruyi.com/uview-plus/album/1.jpg',
            'https://uview-plus.jiangruyi.com/uview-plus/album/2.jpg',
            'https://uview-plus.jiangruyi.com/uview-plus/album/3.jpg',
            'https://uview-plus.jiangruyi.com/uview-plus/album/4.jpg',
            'https://uview-plus.jiangruyi.com/uview-plus/album/7.jpg',
            'https://uview-plus.jiangruyi.com/uview-plus/album/6.jpg',
            'https://uview-plus.jiangruyi.com/uview-plus/album/5.jpg'
        ],
        waitingNumber: '2',
        startTime: '今天17:00',
        rating: 3,
        heat: 3,
        score: 9.2,
        alreadyNumber: 6,
        totalNumber: 8,
        price: 98,
        introduction: '年三十，北道河，村里出了个杀人魔。杀了一个又一个，最后一个杀老婆。七个小孩儿来串门，联起手来把案破。',
        paymentStatus: 2,
        wantPlayStatus: 1
    },])
const scriptData = ref({
    user: {
        avatar: profile,
        name: '张三',
        timestamp: '08.24 15:30'
    },
    content: "剧本非常好，逻辑清晰，没有漏洞啊哈，哈哈哈剧本非常好，逻辑清晰，没有漏洞啊哈，哈哈哈剧本非常好，逻辑清晰，没有漏洞啊哈，哈哈哈剧本非常好，逻辑清晰，没有漏洞啊哈，哈哈哈哈哈,哈哈哈剧本非常,哈哈哈哈哈,哈哈哈剧本非常哈哈哈哈哈,哈哈哈剧本非常",
    images: [xuexiang, xuexiang, xuexiang, xuexiang, xuexiang],
    tags: [
        { name: '剧本评价', type: 'script' },
        { name: 'dm评价', type: 'dm' },
        { name: '第三个带的话题', type: 'other' }
    ],
    position: '北京',
    publishType: 1,
    totalNumber: 5,
    views: 100,
})
const onLike = () => console.log('点赞');
const onComment = () => console.log('评论');
const onShare = () => console.log('分享');
const PublishType: Record<number, string> = {
    0: '发布动态',
    1: '场次评价',
    2: '发表剧本'
}
interface Comment {
    id: string;
    userId: string;
    userName: string;
    userAvatar: string;
    content: string;
    date: string; // 格式化后的日期字符串，例如 "08.24 15:35"
    location: string;
    replies: Comment[]
    myLiked?: boolean
}

const comments = ref<Comment[]>([
    {
        id: '1',
        userId: 'author',
        userName: '啊哈哈哈',
        userAvatar: profile,
        content: '评论评论评论评论评论评论评论评论评论评论评论评论评论评论评论评论评论评论评论评论。',
        date: '08.24 15:35',
        location: '太原',
        myLiked: false,
        replies: [{
            id: '4',
            userId: 'user1',
            userName: '啊哈哈哈',
            userAvatar: profile,
            content: '评论评论评论评论评论评论评论评论评论评论评论评论评论评论评论评论评论评论评论评论。',
            date: '08.24 15:35',
            location: '太原',
            replies: [],
            myLiked: false
        },]

    },
    {
        id: '2',
        userId: 'myId', // 假设这是当前用户的ID
        userName: '啊哈哈哈',
        userAvatar: profile,
        content: '有图片默认放第一个图，有人评价了我发的帖子',
        date: '08.24 15:35',
        location: '太原',
        myLiked: false,
        replies: [{
            id: '5',
            userId: 'user1',
            userName: '啊哈哈哈',
            userAvatar: profile,
            content: '评论评论评论评论评论评论评论评论评论评论评论评论评论评论评论评论评论评论评论评论。',
            date: '08.24 15:35',
            location: '太原',
            myLiked: false,
            replies: []

        },
        {
            id: '6',
            userId: 'user1',
            userName: '啊哈哈哈',
            userAvatar: profile,
            content: '评论评论评论评论评论评论评论评论评论评论评论评论评论评论评论评论评论评论评论评论。',
            date: '08.24 15:35',
            location: '太原',
            myLiked: false,
            replies: []

        },
        {
            id: '7',
            userId: 'user1',
            userName: '啊哈哈哈',
            userAvatar: profile,
            content: '评论评论评论评论评论评论评论评论评论评论评论评论评论评论评论评论评论评论评论评论。',
            date: '08.24 15:35',
            location: '太原',
            myLiked: false,
            replies: []

        },]
    },
    {
        id: '3',
        userId: 'user3',
        userName: '啊哈哈哈',
        userAvatar: profile,
        content: '评论评论评论评论评论评论评论评论评论评论评论评论评论评论评论评论评论评论评论评论。',
        date: '08.24 15:35',
        location: '太原',
        myLiked: false,
        replies: [{
            id: '8',
            userId: 'user1',
            userName: '啊哈哈哈',
            userAvatar: profile,
            content: '评论评论评论评论评论评论评论评论评论评论评论评论评论评论评论评论评论评论评论评论。',
            date: '08.24 15:35',
            location: '太原',
            myLiked: false,
            replies: []

        },]
    }
]);
const isLiked = ref(false) // 全局点赞状态（如文章/视频点赞）

const commentCount = computed(() => comments.value.length)

// 处理主评论点赞
const handleUpdateCommentLike = (commentId: string, liked: boolean) => {
    console.log('用户点赞评论:', commentId, liked)

    updateCommentMyLiked(comments.value, commentId, liked)
}

const updateCommentMyLiked = (comments: Comment[], id: string, liked: boolean): boolean => {
    for (const comment of comments) {
        if (comment.id === id) {
            comment.myLiked = liked
            return true
        }
        // 递归检查 replies
        if (updateCommentMyLiked(comment.replies, id, liked)) {
            return true
        }
    }
    return false
}
// 添加回复到目标（主评论或二级回复）
const addReplyToTarget = (comments: Comment[], targetId: string, newReply: Comment) => {
    for (let comment of comments) {
        if (comment.id === targetId) {
            comment.replies.push(newReply)
            return
        }
        for (let reply of comment.replies) {
            if (reply.id === targetId) {
                reply.replies.push(newReply) // 虽然不显示三级，但数据可存
                return
            }
        }
    }
}
// 处理提交回复
const handleSubmitReply = (replyData: { targetId: string; content: string; newReply: Comment }) => {
    // 添加到对应评论
    addReplyToTarget(comments.value, replyData.targetId, replyData.newReply)
}

// 点击底部“点赞”按钮
const like = () => {
    isLiked.value = !isLiked.value
    // 调用 API 点赞文章/视频等
}
</script>
<template>
    <view class="app-container">
        <!-- 页面主体内容（可滚动） -->
        <scroll-view class="main-content" scroll-y="true">
            <view class="topic-details">
                <!-- 头部：用户信息 -->
                <view class="header">
                    <img :src="scriptData.user.avatar" class="avatar" />
                    <view class="info">
                        <view class="top">
                            <view class="username">{{ scriptData.user.name }}</view>
                            <view class="timePosition">
                                <view class="timestamp">{{ scriptData.user.timestamp }}</view>
                                <span class="dot"></span>
                                <view class="position">{{ scriptData.position }}</view>
                            </view>

                        </view>
                        <view class="bottom">
                            <view class="publishType">{{ PublishType[scriptData.publishType] || '未知类型' }}</view>
                            <up-icon name="eye" :label="scriptData.views" labelSize="12" label-color="#999"></up-icon>
                        </view>
                    </view>

                </view>
                <view class="topic">
                    <topicContent :topic="scriptData" :currentscript="scriptList[0]" :max-content-length="82"
                        :enable-truncation="true" :maxImageCount=0 @like="onLike" @comment="onComment"
                        @share="onShare" />
                </view>
            </view>
            <view class="commentArea">
                <commentArea :comments="comments" :commentCount="commentCount" :user="scriptData.user"
                    @update:comment-like="handleUpdateCommentLike" @submit-reply="handleSubmitReply"></commentArea>
            </view>
        </scroll-view>
        <view class="bottom-fixed">
            <view class="action-buttons">
                <view class="action-button share">
                    <up-icon name="share" size="28"></up-icon>
                    <view>分享</view>
                </view>

                <view class="action-button comment" @click="showReplyInput = true">
                    <up-icon name="chat" size="28"></up-icon>
                    <view>评论</view>
                </view>
                <view class="action-button like" @click="like">
                    <up-icon :name="isLiked ? 'thumb-up-fill' : 'thumb-up'" :color="isLiked ? '#ff0000' : ''"
                        size="28" />
                    <view>{{ isLiked ? '已点赞' : '点赞' }}</view>
                </view>
            </view>
        </view>

        <bottomReplyInput v-model="showReplyInput" placeholder="说点什么..." @submit="handleCommentSubmit" />
    </view>
</template>
<style scoped lang="scss">
@import "@/static/scss/variables.module.scss";

.app-container {
    background-color: $background-color;
}

.topic-details {
    display: flex;
    flex-direction: column;
    padding: 20rpx;
    background-color: #fff;
    gap: 8rpx;
}

.header {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    gap: 10rpx;
    flex: 0;

    .info {
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: 5rpx;

        .top {
            display: flex;
            justify-content: space-between;
            align-items: center;

            .username {
                font-size: 28rpx;
                font-weight: bold;
                color: #333;
            }

            .timePosition {
                flex-shrink: 0;
                display: flex;
                flex-direction: row;
                gap: 8rpx;
                justify-content: flex-start;
                align-items: center;

                /* 垂直居中 */
                .dot {
                    width: 3rpx;
                    height: 3rpx;
                    background-color: rgba($primary-text-color, 0.6);
                    border-radius: 50%;
                }

                .timestamp,
                .position {
                    font-size: 20rpx;
                    line-height: 24rpx;
                    /* 统一行高 */
                    color: #999;
                }

            }

        }

        .bottom {
            display: flex;
            justify-content: space-between;

            .publishType {
                font-size: 20rpx;
                color: #999;
            }
        }
    }
}

.avatar {
    width: 85rpx;
    height: 85rpx;
    border-radius: 50%;

}

.main-content {
    flex: 1;
    height: calc(100vh - 100rpx); // 留出底部导航的高度空间
}

.commentArea {
    margin-top: 10rpx;
    background-color: #fff;
}

.bottom-fixed {
    display: flex;
    flex-direction: column;
    position: fixed;
    bottom: 0;
    width: 100%;
    z-index: 999;
    background-color: #fff;
    border-top: 1rpx solid #eee;
    padding: 20rpx;
    /* 增加内边距 */
    box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.08);
    /* 添加阴影效果 */
    /* H5 平台需要适配浏览器侧边栏（如微信浏览器） */
    /* #ifdef H5 */
    left: var(--window-left);
    right: var(--window-right);
    /* #endif */
}


.action-buttons {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 20px;
    /* 调整按钮之间的间距 */
}

.action-button {
    display: flex;
    align-items: center;
    gap: 5px;
    /* 调整图标和文本之间的间距 */
    font-size: 14px;
    color: #666;
    cursor: pointer;
}

.like {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    align-self: flex-end;
    gap: 6rpx;

}
</style>