<script setup lang="ts">
import { ref, onMounted, reactive } from 'vue';
import { useRoute } from 'vue-router'; // 引入 useRoute 钩子
import topicCard from './topicCard.vue';
import profile from '@/static/images/profile.jpg'
import xuexiang from "@/static/images/cover/xuexiang.jpg";
import type { Script } from '@/types/Script';
import NavSelect from '@/components/jbs/nav-select.vue';
// 定义动态数据的接口
interface DynamicData {
  id: number;
  title: string;
  content: string;
  author: {
    name: string;
    avatar: string;
  };
  publishTime: string; // 格式化后的时间字符串
  images: string[]; // 图片 URL 数组
  hashtags: string[];
  likes: number;
  comments: number;
  shares: number;
}

// 使用 useRoute 获取当前路由信息
const route = useRoute();
// 从路由参数中获取 id
const id = parseInt(route.params.id as string);
//存储获取到的数据
const dynamicData = ref<DynamicData | null>(null);

function goToFind() {
  uni.switchTab({
    url: `/pages/find/index`
  });
}

const scriptList = reactive<Script[]>([
  {
    id: 7,
    title: '雪乡连环杀人事件',
    cover: xuexiang,
    tags: ['推理', '惊悚', '现代', '本格'],
    players: '3男3女',
    duration: '5小时',
    difficulty: '适中',
    address: '山西省太原市茂业一期光影剧本杀',
    distance: '10.0km',
    avatars: [
      'https://uview-plus.jiangruyi.com/uview-plus/album/1.jpg',
      'https://uview-plus.jiangruyi.com/uview-plus/album/2.jpg',
      'https://uview-plus.jiangruyi.com/uview-plus/album/3.jpg',
      'https://uview-plus.jiangruyi.com/uview-plus/album/4.jpg',
      'https://uview-plus.jiangruyi.com/uview-plus/album/7.jpg',
      'https://uview-plus.jiangruyi.com/uview-plus/album/6.jpg',
      'https://uview-plus.jiangruyi.com/uview-plus/album/5.jpg'
    ],
    waitingNumber: '2',
    startTime: '今天17:00',
    rating: 3,
    heat: 3,
    score: 9.2,
    alreadyNumber: 6,
    totalNumber: 8,
    price: 98,
    introduction: '年三十，北道河，村里出了个杀人魔。杀了一个又一个，最后一个杀老婆。七个小孩儿来串门，联起手来把案破。',
    paymentStatus: 2,
    wantPlayStatus: 1
  },])
const scriptData = ref({
  user: {
    avatar: profile,
    name: '张三',
    timestamp: '08.24 15:30'
  },
  content: "剧本非常好，逻辑清晰，没有漏洞啊哈，哈哈哈剧本非常好，逻辑清晰，没有漏洞啊哈，哈哈哈剧本非常好，逻辑清晰，没有漏洞啊哈，哈哈哈剧本非常好，逻辑清晰，没有漏洞啊哈，哈哈哈哈哈,哈哈哈剧本非常,哈哈哈哈哈,哈哈哈剧本非常哈哈哈哈哈,哈哈哈剧本非常",
  images: [xuexiang, xuexiang, xuexiang, xuexiang, xuexiang],
  tags: [
    { name: '剧本评价', type: 'script' },
    { name: 'dm评价', type: 'dm' },
    { name: '第三个带的话题', type: 'other' }
  ],
  publishType: 1,
  totalNumber: 5
})
const onLike = () => console.log('点赞');
const onComment = () => console.log('评论');
const onShare = () => console.log('分享');
const goToDetails = () => {
  uni.navigateTo({
    url: `/pages/find/topic/details`
  });
};
</script>
<template>
  <view class="container">
    <up-icon name="arrow-left" @click="goToFind()"></up-icon>
    <!-- 头部 -->
    <view class="header-gradient">
      <view class="top">
        <h2># 剧本杀初体验 #</h2>
        <p>“这是我玩的第一个本，因为那时候对剧本杀不是很了解...”</p>
      </view>
      <view class="bottom">
        <view class="user-info">
          <up-avatar-group :urls="scriptList[0].avatars" size="24" gap="0.4" class="script-kill-avatar" /><span>等<span
              class="number">{{
                scriptData.totalNumber }}</span>人参与</span>
        </view>
        <up-icon name="share" size="28"></up-icon>
      </view>
    </view>

    <!-- 内容区域 -->
    <NavSelect class="ppd-l" :nav="['推荐', '最新']" flex="start" />
    <view class="topic" @click="goToDetails()">
      <topicCard :topic="scriptData" :currentscript="scriptList[0]" :max-content-length="82" :enable-truncation="true"
        :maxImageCount=0 @like="onLike" @comment="onComment" @share="onShare" />
    </view>

  </view>
</template>
<style scoped lang="scss">
@import "@/static/scss/variables.module.scss";

.container {
  padding: 1rem;
  background-color: #f9f9f9;
}

.header-gradient {
  background: linear-gradient(135deg, rgba($main-color, 1), rgba($main-color, 0.2));
  color: white;
  border-radius: 10rpx;
  padding: 0.5rem;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.header-gradient h2 {
  font-size: 1.5rem;
}

.header-gradient p {
  font-size: 0.9rem;
}

.bottom {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 8rpx;
}

.user-info {
  align-self: flex-start;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 8rpx;
}

.user-info span {
  font-size: 0.8rem;
  color: black;
  background-color: rgba(255, 255, 255, 0.948);
  padding: 0.1rem 0.2rem;
  border-radius: 1rem;

  .number {
    color: rgba($main-color, 0.8)
  }

}
</style>