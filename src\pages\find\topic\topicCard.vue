<script setup lang="ts">
import { ref } from 'vue';

import type { <PERSON>ript } from '@/types/Script';
import topicContent from './topicContent.vue';
// 定义用户类型
interface User {
    avatar: string;
    name: string;
    timestamp: string;
}
interface Tag {
    name: string;
    type: string;
}

// 定义组件 props 类型
interface topicData {
    user: User;
    content: string;
    images: string[];
    tags: Tag[];
    publishType: number;
}
const PublishType: Record<number, string> = {
    0: '发布动态',
    1: '场次评价',
    2: '发表剧本'
}

interface Props {
    topic: topicData;
    currentscript: Script;
    // 可配置内容显示
    maxContentLength?: number;//正文文字数
    enableTruncation?: boolean;//是否限制最大字数
    maxImageCount?: number | null; // 最大图片数量，null 表示不限制
}

const props = withDefaults(defineProps<Props>(), {
    maxContentLength: 85,
    enableTruncation: true,
    maxImageCount:3
});
// 定义事件
const emit = defineEmits<{
    (e: 'click'): void;        
    (e: 'like'): void;
    (e: 'comment'): void;
    (e: 'share'): void;
}>();


// 点赞数和点赞状态
const likes = ref(0);
const isLiked = ref(false); // 新增：记录是否已点赞

// 事件处理函数
const like = () => {
    if (isLiked.value) {
        // 如果已经点赞，再次点击则取消点赞
        likes.value--;
        isLiked.value = false;
    } else {
        // 如果未点赞，点击则点赞
        likes.value++;
        isLiked.value = true;
    }
    emit('like');
};

const comment = () => {
    emit('comment');
};

const share = () => {
    emit('share');
};


</script>

<template>
    <view class="topic-card" @click="$emit('click')">
        <!-- 头部：用户信息 -->
        <view class="header">
            <img :src="props.topic?.user.avatar" class="avatar" />
            <view class="info">
                <view class="top">
                    <view class="username">{{ props.topic?.user.name }}</view>
                    <view class="timestamp">{{ props.topic?.user.timestamp }}</view>
                </view>
                <view class="bottom">
                    <view class="publishType">{{ PublishType[props.topic?.publishType] || '未知类型' }}</view>
                </view>
            </view>

        </view>
        <topicContent :topic="topic" :currentscript="currentscript" :max-content-length="maxContentLength"
            :enable-truncation="enableTruncation" :max-image-count="maxImageCount"></topicContent>


        <view class="action-buttons">
            <view class="action-button like" @click.stop="like">
                <up-icon name="thumb-up" :color="isLiked ? '#ff0000' : ''" size="28"></up-icon>
                <view>{{ likes
                }}</view>
                <view>点赞</view>

            </view>
            <view class="action-button comment">
                <up-icon name="chat" size="28"></up-icon>
                <view @click.stop="comment">评论</view>
            </view>
            <view class="action-button share">
                <up-icon name="share" size="28"></up-icon>
                <view @click.stop="share">分享</view>
            </view>
        </view>
    </view>
</template>

<style scoped lang="scss">
@import "@/static/scss/variables.module.scss";

.topic-card {
    display: flex;
    flex-direction: column;
    border: 1px solid #e0e0e0;
    border-radius: 12rpx;
    padding: 20rpx;
    background-color: #fff;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.08);
    gap: 8rpx;
}

.header {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    gap: 10rpx;
    flex: 0;

    .info {
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: 5rpx;

        .top {
            display: flex;
            justify-content: space-between;
            align-items: center;

            .username {
                font-size: 28rpx;
                font-weight: bold;
                color: #333;
            }

            .timestamp {
                font-size: 20rpx;
                color: #999;
            }
        }

        .bottom {
            display: flex;
            justify-content: flex-start;

            .publishType {
                font-size: 20rpx;
                color: #999;
            }
        }
    }
}

.avatar {
    width: 85rpx;
    height: 85rpx;
    border-radius: 50%;

}

.action-buttons {
    display: flex;
    align-items: center;
    gap: 20px;
    /* 调整按钮之间的间距 */
}

.action-button {
    display: flex;
    align-items: center;
    gap: 5px;
    /* 调整图标和文本之间的间距 */
    font-size: 14px;
    color: #666;
    cursor: pointer;
}

.action-button.like {
    flex: 1;
    display: flex;
    align-items: center;
    gap: 6rpx;

}
</style>