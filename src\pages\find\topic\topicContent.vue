<script setup lang="ts">
import { ref, computed } from 'vue';
import Card from "@/components/jbs/card.vue";
import type { Script } from '@/types/Script';
// 定义用户类型
interface User {
    avatar: string;
    name: string;
    timestamp: string;
}
interface Tag {
    name: string;
    type: string;
}

// 定义组件 props 类型
interface topicData {
    user: User;
    content: string;
    images: string[];
    tags: Tag[];
    publishType: number;
}
const PublishType: Record<number, string> = {
    0: '发布动态',
    1: '场次评价',
    2: '发表剧本'
}



// 定义 props
interface config {
    topic: topicData;
    currentscript: Script;
    // 可配置内容显示
    maxContentLength?: number;//正文文字数
    enableTruncation?: boolean;//是否限制最大字数
    maxImageCount?: number | null; // 最大图片数量，null 表示不限制
}

const isExpanded = ref(false);
const needsTruncation = computed(() => {
    return props.enableTruncation && (props.topic?.content?.length || 0) > props.maxContentLength;
});
const toggleExpand = () => {
    isExpanded.value = !isExpanded.value;
};
//当前显示的内容
const displayContent = computed(() => {
    const content = props.topic?.content || '';
    if (!props.enableTruncation) return content;
    if (isExpanded.value) return content;
    return content.slice(0, props.maxContentLength);
});
const displayedImages = computed(() => {
    const images = props.topic?.images || [];
    if (!images.length) return [];

    // 如果 maxImageCount 为 null/undefined/0，表示不限制
    if (!props.maxImageCount) {
        return images;
    }

    return images.slice(0, props.maxImageCount);
});
// 接收 props
const props = withDefaults(defineProps<config>(), {
    maxContentLength: 85,
    enableTruncation: true,
    maxImageCount: 3 // 默认最多显示 3 张图
});

</script>

<template>
    <view class="topic-content" @click="$emit('click')">

        <view class="scriptCard" v-if="props.topic?.publishType != 0">
            <Card :script="props.currentscript" displayMode="ListTopics"
                style="background-color: #F1F6FF;height: 100%;width: 100%;" />
        </view>
        <view class="rating" v-if="props.topic?.publishType == 2 && props.topic?.publishType != 2">
            <!-- 总体评分 -->
            <view class="overall-rating">
                <span>总体：</span>
                <up-rate :count="5" v-model="props.currentscript.rating" class="script-kill-rate" :gutter="2" :size="10"
                    :activeColor="'#ffa500'" readonly />
            </view>
        </view>
        <!-- 正文内容 -->
        <view class="content" v-if="props.topic?.publishType != 2">
            <view class="content-text" :class="{ expanded: isExpanded }">
                {{ displayContent }}
                <template v-if="needsTruncation && !isExpanded">
                    <span class="ellipsis">...</span>
                    <span @click.stop="toggleExpand" class="expand-button">展开 &gt;&gt;</span>

                </template>
                <span v-if="isExpanded" @click.stop="toggleExpand" class="expand-button-arrow-up">收起<up-icon
                        name="arrow-up" color="#649BFB"></up-icon>
                </span>
            </view>

        </view>

        <!-- 图片列表 -->
        <view v-if="props.topic?.images.length > 0 && props.topic?.publishType != 2" class="images">
            <view v-for="(img, index) in displayedImages" :key="index" class="image-wrapper">
                <img :src="img" class="image" />

            </view>
        </view>

        <!-- 标签 -->
        <view v-if="props.topic?.tags.length > 0 && props.topic?.publishType != 2" class="tags">
            <span v-for="(tag, index) in props.topic?.tags" :key="index" :class="['tag', tag.type]">
                <span>#</span> <span>{{ tag.name }}</span> <span>#</span>
            </span>
        </view>

    </view>
</template>

<style scoped lang="scss">
@import "@/static/scss/variables.module.scss";


.topic-content {
    display: flex;
    flex-direction: column;
    background-color: #fff;
    gap: 10rpx;
}

.scriptCard {
    background-color: rgba($main-color, 0.2);
    height: 200rpx;
    width: 100%;
}



.rating {
    display: flex;
    align-items: center;
}

.overall-rating {
    display: flex;
    justify-content: flex-start;
    align-items: center;
}


.content-text {
    font-size: 28rpx;
    line-height: 1.5;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    word-break: break-word;
    position: relative;
    /* 添加相对定位 */
}


.content-text.expanded {
    line-clamp:unset !important;
    -webkit-line-clamp: unset !important;
    overflow: visible !important;
    display: block !important;
}

.expand-button {
    color: #666;
    cursor: pointer;
    position: absolute;
    bottom: 0;
    right: 0;
    background-color: transparent;
    padding-left: 10rpx;
    color: rgba($main-color, 0.8);
}

.expand-button-arrow-up {
    color: rgba($main-color, 0.8);
    cursor: pointer;
    position: absolute;
    bottom: 0;
    right: 0;
    background-color: transparent;
    padding-left: 10rpx;
    display: inline-flex;
    align-items: center;
    /* 垂直居中对齐 */

}

.images {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 10rpx;
  width: 100%;
}

.image-wrapper {
  position: relative;
  height: 0;
  padding-bottom: 100%;           // 正方形
  border-radius: 8rpx;
  overflow: hidden;
  background-color: #f0f0f0;      // 防止空白图
}

.image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.more-badge {
  position: absolute;
  right: 10rpx;
  bottom: 10rpx;
  background-color: rgba(0, 0, 0, 0.6);
  color: #fff;
  font-size: 24rpx;
  padding: 6rpx 12rpx;
  border-radius: 6rpx;
  font-weight: bold;
  z-index: 1;
}
.tags {
    display: flex;
    flex-wrap: wrap;
    gap: 10rpx;
    justify-content: flex-start;

    .tag {

        font-size: 24rpx;
        border-radius: 30rpx;
        border: 1rpx solid rgba($main-color, 0.2);
        padding: 0 10rpx;

    }

    .tag.dm {
        color: $main-color;
        background-color: rgba($main-color, 0.1);
    }

    .tag.script {
        color: $main-color;
        background-color: rgba($main-color, 0.1);
    }

    .tag.other {
        color: rgba($auxiliary-color-orange, 0.8);
        background-color: rgba($auxiliary-color-orange, 0.1);
    }
}
</style>