<script setup lang="ts">
import { ref, onMounted, watch } from 'vue';

interface Script {
    id: number;
    title: string;
    cover: string;
    tags: string[];
    players: string;
    duration: string;
    difficulty: string;
    rating: number;
    introduction: string;
}

const props = withDefaults(defineProps<{
    scripts: Script[];
}>(), {
    scripts: () => []
})

watch(() => props.scripts, (newScripts) => {
    if (newScripts.length > 0 && !selectedScript.value) {
        selectedScript.value = newScripts[0]; // 默认选中第一个剧本
    }
});
const selectedScript = ref<Script | null>(null);

const selectScript = (index: number) => {
    selectedScript.value = props.scripts[index];
};

onMounted(() => {
    if (props.scripts.length > 0) {
        selectedScript.value = props.scripts[0]; // 默认选中第一个剧本
    }
});
</script>
<template>
    <view class="card-box" v-if="selectedScript">
        <view class="img-box">
            <img :src="selectedScript.cover" class="selected-script-img" />
        </view>
        <view class="script-details">
            <view class="script-covers">
                <img v-for="(script, index) in scripts" :key="index" :src="script.cover" @click="selectScript(index)"
                    class="script-cover" :class="{ active: selectedScript.id === script.id }" />
            </view>
            <view class="right-bottom">
                <view class="title-rating-container">
                    <view class="script-kill-title">{{ selectedScript.title }}</view>
                    <view class="script-kill-score">评分: {{ selectedScript.rating }}</view>
                </view>
                <view class="up-tag-price">
                    <up-tag v-for="(item, index) in selectedScript.tags" :text="item" :key="index"
                        borderColor="transparent" class="tag-item" size="mini" color="#867676" plain plainFill />
                </view>
                <view class="script-kill-text">
                    <view class="text-item">
                        <up-icon name="account-fill" color="#e8f2ff" size="18" />
                        <text class="text-item-text">{{ selectedScript.players }}</text>
                    </view>
                    <text class="separator">·</text>
                    <view class="text-item">
                        <up-icon name="clock-fill" color="#e8f2ff" size="18" />
                        <text class="text-item-text">{{ selectedScript.duration }}</text>
                    </view>
                    <text class="separator">·</text>
                    <view class="text-item">
                        <up-icon name="hourglass-half-fill" color="#e8f2ff" size="18" />
                        <text class="text-item-text">{{ selectedScript.difficulty }}</text>
                    </view>
                </view>
                <view class="script-kill-introduction">
                    <text class="brief-introduction">{{ selectedScript.introduction }}</text>
                </view>
            </view>
        </view>
    </view>
</template>
<style scoped lang="scss">
@import "@/static/scss/leyout.scss";
$height: 400rpx;

.card-box {
    @extend .flex-rsb;
    padding: $sp-page;
    background-color: transparent;
    height: $height;

    .img-box {
        @extend .flex-center;
        border-radius: 20rpx 20rpx 0 20rpx;
        background: #ffffff;
        padding: $sp-small;
        z-index: 1;

        .selected-script-img {
            width: 225rpx;
            height: calc($height - $sp-page * 2 - $sp-small * 2);
            border-radius: 10rpx 10rpx 10rpx 10rpx;
        }
    }
}

// 右侧详情
.script-details {
    @extend .flex-csb;
    width: calc(100% - $sp-small * 2 - 225rpx);
    padding-top: $sp-small;
    height: calc($height - $sp-page * 2);

    .script-covers {
        display: flex;
        justify-content: space-between;
        overflow-x: auto;
        gap: 10rpx;
        width: 100%;

        .script-cover {
            width: 50rpx;
            border-radius: $border-radius-small;
            transition: all 0.3s ease;
            border: 2rpx solid #e0e0e0;

            &.active {
                border: 3rpx solid #ffffff;
                box-shadow: 0 0 5rpx #ffffff;
                border-radius: $border-radius-small;
            }
        }
    }

    .right-bottom {
        background: #ffffff;
        width: 100%;
        padding: $sp-small;


        .title-rating-container {
            display: flex;
            gap: 10rpx;

            .script-kill-title {
                font-size: 32rpx;
                color: $primary-text-color;
                font-weight: bold;
                display: inline-block;
            }

            .script-kill-score {
                font-size: 28rpx;
                align-self: flex-end;
                color: #909399;
                font-weight: 300;
            }
        }

        .up-tag-price {
            display: flex;
            align-items: baseline;

            .tag-item {
                margin-top: 8rpx;
                margin-right: $sp-small;
                color: $primary-text-color;
            }
        }

        .script-kill-text {
            display: flex;
            align-items: center;
            margin-top: 8rpx;
            font-size: 26rpx;
            color: $primary-text-color;

            .text-item {
                display: flex;
                align-items: center;
                margin-right: 10rpx;
                gap: 6rpx;
            }

            .separator {
                margin: 0 6rpx;
            }
        }

        .script-kill-introduction {
            .brief-introduction {
                display: -webkit-box;
                -webkit-box-orient: vertical;
                font-size: 26rpx;
                line-clamp: 2;
                -webkit-line-clamp: 2;
                overflow: hidden;
                text-overflow: ellipsis;
                color: #867676;
            }
        }
    }


}
</style>