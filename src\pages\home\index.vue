<script setup lang="ts">
import { reactive, ref, onMounted } from "vue";
import banner01 from "@/static/images/banner/banner01.jpg";
import banner02 from "@/static/images/banner/banner02.jpg";
import banner05 from "@/static/images/banner/banner05.png";
import banner04 from "@/static/images/banner/banner04.png";
import banner06 from "@/static/images/banner/banner06.jpg";
import banner07 from "@/static/images/banner/banner07.jpg";
import btn1 from "@/static/images/button/btn1.png";
import btn2 from "@/static/images/button/btn2.png";
import btn3 from "@/static/images/button/btn3.png";
import TimeSelect from "./time-select.vue";
import Card from "@/components/jbs/card.vue";
import ChooseCard from "@/pages/home/<USER>";
import TitleSection from "./title-section.vue";
import Positioning from "@/components/jbs/positioning.vue";
import TagSelect from "@/components/jbs/tag-select.vue";
import type { Script } from '@/types/Script';
import { getData } from "../constVar";

const swiperlist = reactive([banner04, banner05]);
const button = reactive([
  {
    text: "邀新有礼",
    background:
      "linear-gradient(180deg, rgba(61, 108, 252, 0.6) 0%, rgba(61, 108, 252, 0.2) 100%)",
    image: btn1,
  },
  {
    text: "抽签",
    background:
      "linear-gradient(180deg, rgba(253, 90, 84, 0.6) 0%, rgba(253, 90, 84, 0.2) 100%)",
    image: btn2,
  },
  {
    text: "领卡卷红包",
    background:
      "linear-gradient(180deg, rgba(255, 180, 0, 0.6) 0%, rgba(255, 180, 0, 0.2) 100%)",
    image: btn3,
  },
]);
const scriptList = ref<Script[]>([]);

onMounted(async () => {
  scriptList.value = await getData();
});
</script>
<template>
  <view class="app-container">
    <view class="top-container ppd-t">
      <Positioning class="cmg-b" />
      <view class="banner-container">
        <up-swiper :list="swiperlist" previousMargin="30" nextMargin="30" circular :autoplay="false" radius="5"
          height="240rpx" bgColor="transparent" imgMode="scaleToFill" />
      </view>
      <view class="button-container">
        <view class="button-item" v-for="(item, index) in button" :key="index" :style="{ background: item.background }">
          <text>{{ item.text }}</text>
          <image :src="item.image" />
          <image class="bg" :src="item.image" />
        </view>
      </view>
    </view>
    <TitleSection title="找剧本" subTitle="（附近门店可玩）" butText="查看全部" />
    <view class="find-script-button">
      <image :src="banner06" mode="scaleToFill" />
      <image :src="banner07" mode="scaleToFill" />
    </view>
    <ChooseCard :scripts="scriptList" />
    <TitleSection title="在线拼场" class="cmg-b" />
    <view class="card-page-top">
      <TimeSelect class="ppd-l cmg-b" />
      <TagSelect class="ppd-l" />
    </view>
    <Card :script="script" class="cmg-b" displayMode="HOME" v-for="(script, index) in scriptList" :key="index" />
  </view>
</template>
<style scoped lang="scss">
@import "@/static/scss/leyout.scss";

// 三个卡片按钮
.button-container {
  @extend .flex-rsb;
  gap: $sp-small;
  padding: $sp-vertical $sp-page;

  .button-item {
    @extend .flex-csb;
    position: relative;
    height: 140rpx;
    flex: 1;
    color: #ffffff;
    border-radius: $border-radius-medium;

    text {
      z-index: 2;
      margin-top: $sp-small;
      font-size: 28rpx;
      font-weight: bold;
    }

    image {
      z-index: 2;
      width: 60%;
      height: 60%;
    }

    .bg {
      position: absolute;
      z-index: 1;
      bottom: 0;
      width: 68%;
      height: 95%;
      opacity: 0.8;
      filter: blur(6rpx);
    }
  }
}

// 找剧本
.find-script-button {
  @extend .flex-rsb;
  gap: $sp-small;
  padding: $sp-vertical $sp-page;

  image {
    height: 160rpx;
    flex: 1;
    border-radius: $border-radius-medium;
  }
}
</style>
