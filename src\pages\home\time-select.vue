<script lang="ts" setup>
import { reactive } from 'vue';
const active = defineModel({
    type: String,
    default: '02.16'
});
const timelist = reactive([
    { week: '今天', day: '02.16' },
    { week: '明天', day: '02.17' },
    { week: '周六', day: '02.18' },
    { week: '周日', day: '02.19' },
    { week: '周一', day: '02.20' },
    { week: '周二', day: '02.21' },
    { week: '周三', day: '02.22' }
]);

</script>
<template>
    <view class="time-group">
        <view class="time" v-for="(time, index) in timelist" :key="index" :class="{ 'is-active': time.day === active }"
            @tap="active = time.day">
            <h1>{{ time.week }}</h1>
            <text>{{ time.day }}</text>
        </view>
    </view>
</template>
<style scoped lang="scss">
@import "@/static/scss/leyout.scss";

.time-group {
    display: flex;
    gap: $sp-card;
    overflow-x: auto;

    .time {
        @extend .flex-csb;
        padding: $sp-card;
        border-radius: $border-radius-medium;

        &.is-active {
            background-color: transparentize($auxiliary-color-gray, 0.5);
        }

        h1 {
            font-size: 26rpx;
            font-weight: 600;
        }

        text {
            display: block;
        }
    }
}
</style>