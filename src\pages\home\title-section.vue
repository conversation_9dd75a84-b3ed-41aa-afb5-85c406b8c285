<script lang="ts" setup>
import RightCircle from '@/static/images/icon/right-circle.png'
defineProps<{
    title: string;
    subTitle?: string;
    butText?: string;
}>();
</script>
<template>
    <view class="find-script">
        <view class="title">
            <h1>{{ title }}</h1>
            <text v-show="!!subTitle">{{ subTitle }}</text>
        </view>
        <view class="look-all" v-show="!!butText">
            <text>{{ butText }}</text>
            <image :src="RightCircle" mode="scaleToFill" />
        </view>
    </view>
</template>
<style scoped lang="scss">
@import "@/static/scss/leyout.scss";

.find-script {
    @extend .flex-rsb;
    padding: 0 $sp-page;
    position: relative;

    &::before {
        @extend .small-block;
        content: '';
        position: absolute;
        bottom: 0;
        left: $sp-page;
    }

    .title {
        display: flex;
        justify-content: center;
        align-items: end;
        gap: $sp-small;

        h1 {
            font-size: 32rpx;
            z-index: 2;
            margin-left: 16rpx;
        }

        text {
            font-size: 24rpx;
            color: $secondary-text-color;
        }
    }


    .look-all {
        @extend .flex-center;
        width: 160rpx;
        height: 40rpx;
        background: #ffffff;
        border-radius: $border-radius-medium;

        text {
            display: block;
            font-size: 24rpx;
        }

        image {
            height: 30rpx;
            width: 30rpx;
        }
    }
}
</style>