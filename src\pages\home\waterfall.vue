<script setup>
import { ref, reactive, onMounted } from 'vue'
import xuexiang from '@/static/images/cover/xuexiang.jpg'
import positioning from '@/static/images/icon/positioning.png'

// 定义响应式数据
const loadStatus = ref('loadmore')
const flowList = ref([])
const uWaterfallRef = ref(null)

// 商品数据列表
const list = [
  {
    price: 35,
    title: '雪乡连环杀人事件',
    appraise: '该剧本杀适合广泛的玩家群体，尤其是新手玩家，提供了欢乐和惊悚的体验。',
    image: xuexiang,
    src: 'https://uview-plus.jiangruyi.com/uview-plus/album/1.jpg',
    userName: '张三',
    distance: '1.5km',
    business: '店家店家店家店家店家',
    popularRreviews: 1
  },
  {
    price: 35,
    title: '雪乡连事件',
    appraise: '游戏的推理成分较弱，但整体体验非常愉快，适合大多数玩家参与。',
    image: xuexiang,
    src: 'https://uview-plus.jiangruyi.com/uview-plus/album/1.jpg',
    business: '店家店家店家店家店家',
    userName: '李四',
    distance: '15km',
    popularRreviews: 1
  },
  {
    price: 35,
    title: '雪人事件',
    appraise: '游戏的推理成分较弱，但整体体验非常愉快，适合大多数玩家参与。',
    image: '',
    src: 'https://uview-plus.jiangruyi.com/uview-plus/album/1.jpg',
    userName: '菜五',
    distance: '1.5km',
    popularRreviews: 0
  },
]

// 移除指定项
const remove = (id) => {
  uWaterfallRef.value.remove(id)
}

// 页面加载时添加初始数据
onMounted(() => {
  // 直接使用 list 数据，不添加随机数据
  flowList.value = list.map((item, index) => ({
    ...item,
    id: index // 添加唯一标识符
  }))
})

// 触底加载更多（可以保留空函数或删除相关调用）
const onReachBottom = () => {
  // 不执行任何操作，因为我们不需要加载更多数据
}

// 导出需要在模板中使用的函数
defineExpose({
  onReachBottom
})
</script>

<template>
  <view class="wrap">
    <up-waterfall v-model="flowList" ref="uWaterfallRef">
      <template v-slot:left="{ leftList }">
        <view class="demo-warter" v-for="(item, index) in leftList" :key="item.id">
          <view class="image-container" v-if="item.image !== '' || item.popularRreviews === 1">
            <view v-if="item.popularRreviews == 1" class="script-kill-tag">评价热帖</view>
            <image :src="item.image" class="script-kill-img" v-if="item.image"></image>
            <view class="script-kill-button" v-if="item.popularRreviews == 1">
              <up-icon name="minus-square-fill" color="#ffffff" size="15" class="icon-minus-square"></up-icon>
              <text class="script-kill-button-text">{{ item.title }}</text>
              <up-icon name="arrow-right" color="#ffffff" size="12" class="icon-arrow-right"></up-icon>
            </view>
          </view>
          <view class="script-kill-appraise">
            {{ item.appraise }}
          </view>
          <view class="script-kill-business" v-if="item.popularRreviews == 1">
            <up-icon name="home-fill" color="#409EFF" size="12" class="icon-minus-home"></up-icon>
            <text class="script-kill-business-text">{{ item.business }}</text>
            <up-icon name="arrow-right" color="rgb(160, 154, 136)" size="12" class="icon-arrow-right"></up-icon>
          </view>
          <view class="script-kill-bottom">
            <up-avatar :src="item.src" :size="25" class="script-kill-avatar"></up-avatar>
            <view class="script-kill-userName">{{ item.userName }}</view>
            <view class="positioning-distance">
              <image :src="positioning" class="script-kill-positioning"></image>
              <view class="script-kill-distance">{{ item.distance }}</view>
            </view>
          </view>
        </view>
      </template>
      <template v-slot:right="{ rightList }">
        <view class="demo-warter" v-for="(item, index) in rightList" :key="item.id">
          <view v-if="item.image !== '' || item.popularRreviews === 1" class="image-container">
            <view v-if="item.popularRreviews == 1" class="script-kill-tag">评价热帖</view>
            <image :src="item.image" class="script-kill-img" v-if="item.image"></image>
            <view class="script-kill-button" v-if="item.popularRreviews == 1">
              <up-icon name="minus-square-fill" color="#ffffff" size="15" class="icon-minus-square"></up-icon>
              <text class="script-kill-button-text">{{ item.title }}</text>
              <up-icon name="arrow-right" color="#ffffff" size="12" class="icon-arrow-right"></up-icon>
            </view>
          </view>
          <view class="script-kill-appraise">
            {{ item.appraise }}
          </view>
          <view class="script-kill-business" v-if="item.popularRreviews == 1">
            <up-icon name="home-fill" color="#409EFF" size="12" class="icon-minus-home"></up-icon>
            <text class="script-kill-business-text">{{ item.business }}</text>
            <up-icon name="arrow-right" color="rgb(160, 154, 136)" size="12" class="icon-arrow-right"></up-icon>
          </view>
          <view class="script-kill-bottom">
            <up-avatar :src="item.src" :size="25" class="script-kill-avatar"></up-avatar>
            <view class="script-kill-userName">{{ item.userName }}</view>
            <view class="positioning-distance">
              <image :src="positioning" class="script-kill-positioning"></image>
              <view class="script-kill-distance">{{ item.distance }}</view>
            </view>
          </view>
        </view>
      </template>
    </up-waterfall>
  </view>
</template>

<style lang="scss" scoped>
@import "@/static/scss/leyout.scss";

.wrap {

  .demo-warter {
    border-radius: 16rpx;
    margin: $sp-small;
    background-color: #ffffff;
    position: relative;

    .image-container {
      position: relative;
      min-height: 90rpx;
    }
  }

  .script-kill-tag {
    position: absolute;
    top: 4rpx;
    right: 5rpx;
    border-radius: 0 12rpx 0 12rpx;
    z-index: 1;
    background: linear-gradient(to right, rgb(149, 161, 255), rgb(255, 222, 112));
    font-size: 20rpx;
    padding: 6rpx 12rpx;
    color: white;
    font-weight: bold;
    font-style: italic;
  }

  .script-kill-button {
    @extend .flex-center;
    position: absolute;
    bottom: 20rpx;
    left: 10rpx;
    z-index: 2;
    width: auto;


    .icon-minus-square {
      background: rgb(255, 222, 133);
      height: 30rpx;
      width: 30rpx;
      justify-content: center;
      border-radius: 4rpx 0 0 4rpx;
    }

    .icon-arrow-right {
      background: rgb(115, 118, 122);
      height: 30rpx;
      justify-content: center;
      border-radius: 0 4rpx 4rpx 0;
    }

    .script-kill-button-text {
      display: flex;
      font-size: 21rpx;
      color: #ffffff;
      background: rgb(115, 118, 122);
      height: 30rpx;
      align-self: center;
    }
  }

  .script-kill-img {
    border-radius: 12rpx 12rpx 0 0;
  }

  .script-kill-appraise {
    font-weight: 500;
    padding: 0 30rpx;
    font-size: 24rpx;
    color: $primary-text-color;
    margin-top: 5rpx;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    line-clamp: 2;
    -webkit-line-clamp: 2;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .script-kill-business {
    display: flex;
    align-items: center;
    padding: 0rpx 0rpx;
    margin-top: 5rpx;
    background: rgb(236, 245, 255);
    border-radius: 16rpx;
    margin-left: 30rpx;
    width: fit-content;
    max-width: 300rpx;
    gap: 4rpx;

    .icon-minus-home {
      background: #ffffff;
      border: 1px solid rgb(236, 245, 255);
      border-radius: 50%;
    }

    .script-kill-business-text {
      font-size: 20rpx;
    }
  }

  .script-kill-bottom {
    display: flex;
    align-items: center;
    padding: 10rpx 30rpx;

    .script-kill-userName {
      font-size: 24rpx;
      margin-left: 8rpx;
    }

    .positioning-distance {
      display: flex;
      align-items: center;
      margin-left: auto;

      .script-kill-distance {
        font-size: 24rpx;
      }
    }

    .script-kill-positioning {
      height: 24rpx;
      width: 24rpx;
    }
  }
}
</style>