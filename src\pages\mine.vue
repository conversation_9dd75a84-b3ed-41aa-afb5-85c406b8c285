<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue';
import config from '@/config.js'
import useUserStore from '@/store/modules/user'
import NavSelect from '@/components/jbs/nav-select.vue';
import dynamic from '@/static/images/mine/dynamic.png';
import like from '@/static/images/mine/like.png';
import mailbox from '@/static/images/mine/mailbox.png';
import msg from '@/static/images/mine/msg.png';
import Card from "@/components/jbs/card.vue";
import { Script } from '@/types/Script';
import { getData } from "./constVar";

const userStore = useUserStore()
const name = userStore.name;

const avatar = ref(userStore.avatar);
const windowHeight = ref(uni.getSystemInfoSync().windowHeight - 50);

uni.$on('refresh', () => {
  avatar.value = userStore.avatar;
})
function handleWantPlay() {
  uni.navigateTo({
    url: '/pages/mine/wantPlay'
  });
}
function handledynamic() {
  uni.navigateTo({
    url: '/pages/mine/dynamic/index'
  });
}
function handleToInfo() {
  uni.navigateTo({
    url: '/pages_mine/pages/info/index'
  });
};
function handleToLogin() {
  uni.reLaunch({
    url: '/pages/login'
  });
};
function handleToAvatar() {
  uni.navigateTo({
    url: '/pages_mine/pages/avatar/index'
  });
};

function handleMessage() {
  uni.navigateTo({
    url: '/pages/mine/message/index'
  });
};
function handleJiaoLiuQun() {
  uni.showToast({
    title: 'QQ群：133713780',
    mask: false,
    icon: "none",
    duration: 1000
  });
};
function handleBuilding() {
  uni.showToast({
    title: '模块建设中~',
    mask: false,
    icon: "none",
    duration: 1000
  });
}
const nav = ref("我的拼场")
const navs = ref(['我的拼场', '我的评价', '收藏店铺']);
const current = computed({
  get: () => {
    return navs.value.indexOf(nav.value);
  },
  set: (value) => {
    nav.value = navs.value[value];
  }
});

const scriptList = ref<Script[]>([]);
const merchantsSettle = () => {
    uni.navigateTo({
        url: `/pages/mine/merchantsSettle`
    });
};
onMounted(async () => {
  scriptList.value = await getData();
});
</script>
<template>
  <view class="mine-container app-container" :style="{ height: `${windowHeight}px` }">
    <!--顶部个人信息栏-->
    <view class="header-section">
      <view class="flex padding justify-between">
        <view class="flex align-center">
          <image v-if="avatar" @click="handleToAvatar" :src="avatar" class="cu-avatar xl round" mode="widthFix" />
          <view v-else class="cu-avatar xl round bg-white">
            <view class="iconfont icon-people text-gray icon"></view>
          </view>
          <view v-if="!name" @click="handleToLogin" class="login-tip">
            点击登录
          </view>
          <view v-else @click="handleToInfo" class="user-info">
            <view class="u_title">
              {{ name }}
            </view>
          </view>
        </view>
        <view @click="handleToInfo" class="flex align-center">
          <text>个人信息</text>
          <view class="iconfont icon-right"></view>
        </view>
      </view>
      <view class="mine-actions grid col-4 text-center">
        <view class="action-item" @click="handleWantPlay">
          <image :src="like" class="icon" />
          <text class="text">我想玩</text>
        </view>
        <view class="action-item" @click="handledynamic">
          <image :src="dynamic" class="icon" />
          <text class="text">动 态</text>
        </view>
        <view class="action-item" @click="handleMessage">
          <image :src="msg" class="icon" />
          <text class="text">消 息</text>
        </view>
        <view class="action-item" @click="handleBuilding">
          <image :src="mailbox" class="icon" />
          <text class="text">草稿箱</text>
        </view>
        <view class="action-item" @click="merchantsSettle">
          <image :src="mailbox" class="icon" />
          <text class="text">商家入驻</text>
        </view>
      </view>
    </view>

    <view class="card-page-top">
      <view class="nav-container">
        <NavSelect :nav="navs" v-model="nav" flex="space-evenly" />
      </view>
      <swiper class="swiper" :duration="500" style="height: 85vh;" :current="current"
        @change="current = $event.detail.current">
        <swiper-item>
          <view class="content">
            <view v-for="(script, index) in scriptList" :key="index">
              <Card :script="script" displayMode="MYFEATURES" />
            </view>
          </view>
        </swiper-item>
        <swiper-item>
          <view class="content">
            <h1>2</h1>
          </view>
        </swiper-item>
        <swiper-item>
          <view class="content">
            <h1>3</h1>
          </view>
        </swiper-item>
      </swiper>
    </view>
  </view>
</template>
<style lang="scss" scoped>
@import "@/static/scss/leyout.scss";

page {
  background-color: #f5f6f7;
}

.mine-container {
  width: 100%;
  height: 100%;


  .header-section {
    padding: 120rpx 15px 0 15px;
    background: linear-gradient(180deg, #93bdff 0%, #f1f6ff 100%);
    color: white;

    .login-tip {
      font-size: 18px;
      margin-left: 10px;
    }

    .cu-avatar {
      border: 2px solid #eaeaea;

      .icon {
        font-size: 40px;
      }
    }

    .user-info {
      margin-left: 15px;

      .u_title {
        font-size: 18px;
        line-height: 30px;
      }
    }
  }

  .mine-actions {
    border-radius: 8px;
    padding: 0 $sp-page ;

    .action-item {
      display: flex;
      flex-direction: column;
      align-items: center;

      .icon {
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 28px;
        width: 68rpx;
        height: 68rpx;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        border-radius: $border-radius-medium;
        background: linear-gradient(95deg, #5271ff 0%, #81c2fe 100%);
      }

      .text {
        color: $auxiliary-color-info;
        display: block;
        font-size: 13px;
        margin: 8px 0px;
      }
    }
  }

  .content-section {
    position: relative;
    top: -50px;
  }
}

.content {
  height: 100%;
  padding: 0 $sp-page;
  display: flex;
  flex-direction: column;
  gap: $sp-card;
  overflow-y: auto;
}
</style>