<script setup lang="ts">
import { defineProps } from 'vue';
import type { Script } from '@/types/Script';
import Card from "@/components/jbs/card.vue";
enum CardDisplayMode {
    HOME = 'HOME',
    MYFEATURES = 'MYFEATURES',
    WANTPALY = 'WANTPALY',
    DYNAMIC = 'DYNAMIC',
    ListTopics = 'ListTopics'
}

const displayContent = {
    Content: ''
}
// 定义组件 props 类型
interface dynamic {
    content: string;
    images: string[];
    tags: string[];
    date: string
}
withDefaults(defineProps<{
    script: Script;
    displayMode?: `${CardDisplayMode}`;
    dynamic: dynamic;
}>(), {
    displayMode: CardDisplayMode.HOME
});

</script>
<template>
    <view class="card-box">
        <view class="date-container" v-if="dynamic.date">
            <view class="dot"></view>
            <view class="datetext">{{ dynamic.date }}</view>
        </view>
        <!-- 图片区：使用插槽 -->
        <slot name="image">
        </slot>
        <view class="card-right-less" v-if="script?.cover && displayContent.Content">
            <Card :script="script" displayMode="DYNAMIC" style="background-color: #F1F6FF;height: 100%;">
            </Card>
        </view>
        <view class="card-right" v-else>
            <view class="title-rating-container" v-if="script?.title">
                <view class="script-kill-button">
                    <up-icon name="minus-square-fill" color="#ffffff" size="15" class="icon-minus-square"></up-icon>
                    <view class="script-right">
                        <text class="script-kill-button-text">{{ script.title }}</text>
                        <up-icon name="arrow-right" color="#ffffff" size="12" class="icon-arrow-right"></up-icon>
                    </view>

                </view>
            </view>
            <view class="content" v-if="dynamic?.content">
                {{ dynamic?.content }}
            </view>
            <!-- 标签 -->
            <view v-if="dynamic?.tags.length > 0" class="tags">
                <span v-for="(tag, index) in dynamic.tags" :key="index"
                    :class="['tag', tag, { 'has-image': script && dynamic.images.length > 0 }]">
                    <span>#</span> <span>{{ tag }}</span> <span>#</span>
                </span>
            </view>
        </view>
    </view>
</template>
<style scoped lang="scss">
@import "@/static/scss/variables.module.scss";

%display-center {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-box {
    padding: $sp-page;
    background-color: white;
    $main-font-color: $primary-text-color;
    $main-margin-top: 8rpx;
    display: flex;
    justify-content: space-between;
    gap: 15rpx;

    .date-container {
        display: flex;
        align-items: start;
        flex: 1;
        justify-content: flex-start;
        flex-wrap: nowrap;

        height: 20rpx;
        gap: 5rpx;

        .dot {
            width: 10rpx;
            height: 10rpx;
            background-color: rgba($main-color, 0.8);
            border-radius: 50%;

            align-self: center;
        }

        .datetext {
            font-size: 24rpx;
            color: black;

            align-self: center;

        }
    }

    .card-right-less {
        flex: 5;
        align-self: flex-start;
        height: 200rpx;
    }

    .card-right {
        flex: 3;
        text-align: left;
        display: flex;
        flex-direction: column;
        gap: 8rpx;
        justify-content: space-between;


        .title-rating-container {
            @extend %display-center;
            align-items: start;
            gap: 10rpx;

            .script-kill-button {
                display: flex;
                align-items: center;
                z-index: 2;
                justify-content: flex-start;
                flex: 2;
                height: 26rpx;


                .icon-minus-square {
                    background: rgb(255, 222, 133);
                    border-radius: 4rpx 0 0 4rpx;
                    height: 30rpx;
                    padding: 5rpx;
                }

                .script-right {
                    border-radius: 0 4rpx 4rpx 0;
                    padding: 5rpx;
                    display: flex;
                    justify-content: space-between;
                    background: rgb(115, 118, 122);
                    height: 30rpx;
                    line-height: 30rpx;
                    font-size: 20rpx;
                    align-items: center;

                    .icon-arrow-right {
                        align-self: center;
                        border-radius: 0 4rpx 4rpx 0;
                    }

                    .script-kill-button-text {
                        display: flex;
                        color: #ffffff;
                        align-self: center;
                    }
                }

            }

        }

        .content {
            display: -webkit-box;
            line-clamp: 2;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .tags {
            display: flex;
            flex-wrap: wrap;
            gap: 10rpx;
            justify-content: flex-start;

            .tag {

                font-size: 24rpx;
                border-radius: 30rpx;
                border: 1rpx solid rgba($main-color, 0.2);
                padding: 0 10rpx;
                color: rgba($auxiliary-color-orange, 0.8);
                background-color: rgba($auxiliary-color-orange, 0.1);

            }

            .tag.has-image {
                color: $main-color;
                /* 黄色 */
                background-color: rgba($main-color, 0.1);
            }

        }
    }
}
</style>
