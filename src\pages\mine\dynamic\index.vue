<script setup lang="ts">
import { ref, onMounted } from 'vue';
import dynamicCard from './dynamic-card.vue';
import type { Script } from '@/types/Script';
import xuexiang from "@/static/images/cover/xuexiang.jpg";
import { getData } from "@/pages/constVar";
const scriptList = ref<Script[]>([]);

onMounted(async () => {
    scriptList.value = await getData();
    // scriptList.value = []
});
const dynamicList = ref([{
    content: "剧本非常好，逻辑清晰，没有漏洞啊哈，哈哈哈剧本非常好，逻辑清晰，没有漏洞啊哈，哈哈哈剧本非常好，逻辑清晰，没有漏洞啊哈，哈哈哈剧本非常好，逻辑清晰，没有漏洞啊哈，哈哈哈哈哈,哈哈哈剧本非常,哈哈哈哈哈,哈哈哈剧本非常哈哈哈哈哈,哈哈哈剧本非常",
    images: [xuexiang, xuexiang, xuexiang, xuexiang, xuexiang, x<PERSON>xia<PERSON>],
    tags: [
        '剧本评价',
        // { name: 'dm评价', type: 'dm' },
        // { name: '第三个带的话题', type: 'other' }
    ],
    date: '8月30日'
}, {

    content: "剧本非常好，逻辑清晰，没有漏洞啊哈，哈哈哈剧本非常好，逻辑清晰，没有漏洞啊哈，哈哈哈剧本非常好，逻辑清晰，没有漏洞啊哈，哈哈哈剧本非常好，逻辑清晰，没有漏洞啊哈，哈哈哈哈哈,哈哈哈剧本非常,哈哈哈哈哈,哈哈哈剧本非常哈哈哈哈哈,哈哈哈剧本非常",
    images: [],
    tags: [
        '剧本评价',
        // { name: 'dm评价', type: 'dm' },
        // { name: '第三个带的话题', type: 'other' }
    ],
    date: '8月30日'
},]

)
</script>

<template>
    <view class="container">

        <up-card :border="false" padding=0 :show-foot="false" :border-radius="12" :head-border-bottom="false"
            margin='10rpx' v-for="(dynamic, index) in dynamicList" :key="index">
            <template #body>
                <view class="card">
                    <dynamicCard :script="scriptList[0]" displayMode="ListTopics" :dynamic="dynamic">
                        <template #image>
                            <up-album v-if="dynamic?.images.length > 0" :urls="dynamic.images" maxCount="4" rowCount="2"
                                singleSize="160" multipleSize="80" />
                        </template>
                    </dynamicCard>
                </view>
            </template>
        </up-card>

    </view>
</template>

<style scoped lang="scss">
@import "@/static/scss/variables.module.scss";
@import "@/static/scss/leyout.scss";
page{
      background-color: $background-color;

}
.card {
    height: 100%;
    width: 100%;
}
</style>