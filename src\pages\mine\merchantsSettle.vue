<script setup lang="ts">
import { ref } from 'vue';

// 表单数据
const form = ref({
  logo: [],
  shopName: '',
  shopLocation: '',
  detailAddress: '',
  contactPhone: '',
  customerServicePhone: '',
  wechatQrCode: [],
  source: '',
  businessLicense: [],
  idCard: [],
  doorImage: []
});

// 表单引用
const merchantForm = ref<any>(null);

// 得知途径选项
const sourceOptions = [
  { id: 1, name: '朋友介绍 ' },
  { id: 2, name: '网络广告' },
  { id: 3, name: '其他' }
];
// 定义验证规则
const rules = {
  shopName: [{ required: true, message: '请输入店铺名称', trigger: 'blur' }],
  shopLocation: [{ required: true, message: '请输入店铺定位', trigger: 'blur' }],
  detailAddress: [{ required: true, message: '请输入详细地址', trigger: 'blur' }],
  contactPhone: [{ required: true, message: '请输入负责人电话', trigger: 'blur' }],
  source: [{ required: true, message: '请选择得知途径', trigger: 'change' }],
  businessLicense: [{ required: true, message: '请上传营业执照', trigger: 'change' }],
  idCard: [{ required: true, message: '请上传法人身份证人像面', trigger: 'change' }],
  doorImage: [{ required: true, message: '请上传门面照片', trigger: 'change' }]
};

// 处理退出逻辑
const handleExit = () => {
  // 处理退出逻辑
  console.log('Exit');
};
// 处理提交逻辑
const handleSubmit = () => {
  if (merchantForm.value) {
    merchantForm.value.validate((valid: boolean) => {
      if (valid) {
        console.log('Submit:', form.value);
      } else {
        console.log('Validation failed');
      }
    });
  }

};

// 微信获取逻辑
const handleWechatGet = () => {
  // 微信获取逻辑
  console.log('WeChat Get');
};


</script>
<template>
  <view class="container">
    <up-form :model="form" ref="merchantForm" :rules="rules" label-width="120">
      <!-- logo/店铺头像 -->
      <up-form-item label="logo/店铺头像">
        <up-upload v-model="form.logo" :max-count="1"></up-upload>
      </up-form-item>

      <!-- 店铺名称 -->
      <up-form-item label="店铺名称 " prop="shopName" required="true">
        <up-input v-model="form.shopName" placeholder="请输入店铺名称" />
      </up-form-item>

      <!-- 店铺定位 -->
      <up-form-item label="店铺定位 " prop="shopLocation" required="true">
        <up-input v-model="form.shopLocation" placeholder="请输入店铺定位" />
      </up-form-item>

      <!-- 详细地址 -->
      <up-form-item label="详细地址 " prop="detailAddress" required="true">
        <up-input v-model="form.detailAddress" placeholder="请输入详细地址" />
      </up-form-item>

      <!-- 负责人电话 -->
      <up-form-item label="负责人电话 " prop="contactPhone" required="true">
        <view class="flex-row">
          <up-input customStyle="flex:3" v-model="form.contactPhone" placeholder="请输入负责人电话" />
          <view class="right">
            <up-button type="primary" size="mini" @click="handleWechatGet">微信获取</up-button>
          </view>
        </view>
      </up-form-item>

      <!-- 客服电话 -->
      <up-form-item label="客服电话（供玩家联系）" label-width="170">
        <view class="flex-row">
          <up-input v-model="form.customerServicePhone" placeholder="不填写默认同负责人电话" />
        </view>
      </up-form-item>

      <!-- 客服微信二维码 -->
      <up-form-item label="客服微信二维码">
        <up-upload v-model="form.wechatQrCode" :max-count="1"></up-upload>
      </up-form-item>

      <!-- 得知途径 -->
      <up-form-item label="得知途径 " prop="source" required="true">
        <view class="flex-row">
          <up-input v-model="form.customerServicePhone"   />
          <up-select v-model="form.source" :options="sourceOptions"></up-select>
        </view>
      </up-form-item>

      <!-- 图片上传区域 -->
      <up-form-item label="营业执照" prop="businessLicense" required="true">
        <up-upload v-model="form.businessLicense" :max-count="1"></up-upload>
      </up-form-item>
      <up-form-item label="法人身份证人像面" prop="idCard" label-width="150" required="true">
        <up-upload v-model="form.idCard" :max-count="1"></up-upload>
      </up-form-item>
      <up-form-item label="门面照片" prop="doorImage" required="true">
        <up-upload v-model="form.doorImage" :max-count="1"></up-upload>
      </up-form-item>
      <view class="bottom">申请入驻代表您同意《剧本杀服务协议》和《隐私政策》</view>
      <!-- 底部按钮 -->
      <view class="buttons">
        <up-button type="default" customStyle="width:200rpx" @click="handleExit" shape="circle"
          color="#999999">退出</up-button>
        <up-button type="primary" customStyle="width:200rpx" @click="handleSubmit" shape="circle">提交</up-button>
      </view>
    </up-form>
  </view>
</template>



<style lang="scss" scoped>
@import "@/static/scss/variables.module.scss";



.u-form-item {
  background-color: white;
  margin: 20rpx 0;
  padding: 15rpx;

  .flex-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 80rpx;

    .right {
      align-self: center;
      flex: 1;
    }
  }
}

.bottom {
  padding: 0 10rpx 100rpx 10rpx;
  color: $secondary-text-color
}

.buttons {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  gap: 10rpx;
  justify-content: space-between;
  z-index: 999;
  margin-bottom: 30rpx;
}
</style>