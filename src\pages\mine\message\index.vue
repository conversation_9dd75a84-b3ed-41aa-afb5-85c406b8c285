<!-- @/components/CommentsList.vue -->
<script setup lang="ts">
import messageCard from './messageCard.vue';
import { ref } from 'vue';

interface Comment {
    id: string;
    userId: string;
    userName: string;
    userAvatar: string;
    content: string;
    date: string; // 格式化后的日期字符串，例如 "08.24 15:35"
    location: string;
}

const comments = ref<Comment[]>([
    {
        id: '1',
        userId: 'user1',
        userName: '啊哈哈哈',
        userAvatar: 'https://example.com/avatar1.jpg',
        content: '评论评论评论评论评论评论评论评论评论评论评论评论评论评论评论评论评论评论评论评论。',
        date: '08.24 15:35',
        location: '太原'
    },
    {
        id: '2',
        userId: 'myId', // 假设这是当前用户的ID
        userName: '啊哈哈哈',
        userAvatar: 'https://example.com/avatar2.jpg',
        content: '有图片默认放第一个图，有人评价了我发的帖子',
        date: '08.24 15:35',
        location: '太原'
    },
    {
        id: '3',
        userId: 'user3',
        userName: '啊哈哈哈',
        userAvatar: 'https://example.com/avatar3.jpg',
        content: '评论评论评论评论评论评论评论评论评论评论评论评论评论评论评论评论评论评论评论评论。',
        date: '08.24 15:35',
        location: '太原'
    }
]);
</script>

<template>
    <view class="comments-list">
        <messageCard v-for="comment in comments" :key="comment.id" :comment="comment" />
    </view>
</template>

<style scoped lang="scss">
.comments-list {
    padding: 20rpx;
}
</style>