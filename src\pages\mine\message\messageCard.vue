<script setup lang="ts">
import { defineProps } from 'vue';

interface Comment {
    id: string;
    userId: string;
    userName: string;
    userAvatar: string;
    content: string;
    date: string; // 格式化后的日期字符串，例如 "08.24 15:35"
    location: string;
}

const props = defineProps<{
    comment: Comment;
}>();
</script>

<template>
    <view class="comment-card">
        <!-- 用户信息 -->
        <view class="user-info">
            <image :src="comment.userAvatar" class="avatar"></image>
            <view class="user-details">
                <text class="username">{{ comment.userName }}</text>
                <text class="date-location">{{ comment.date }} {{ comment.location }}</text>
            </view>
            <text class="reply">回复</text>
        </view>

        <!-- 评论内容 -->
        <view class="content">
            {{ comment.content }}
        </view>

        <!-- 回复区域 -->
        <view class="reply-area">
            <view v-if="comment.userId === 'myId'" class="my-reply">
                <image :src="comment.userAvatar" class="avatar"></image>
                <view class="reply-content">
                    <text class="reply-text">我自己的id:</text>
                    <text class="reply-message">{{ comment.content }}</text>
                </view>
            </view>
        </view>
    </view>
</template>

<style scoped lang="scss">
.comment-card {
    background-color: #f9f9f9;
    border-radius: 10rpx;
    padding: 20rpx;
    margin-bottom: 20rpx;

    .user-info {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 10rpx;

        .avatar {
            width: 60rpx;
            height: 60rpx;
            border-radius: 50%;
            margin-right: 10rpx;
        }

        .user-details {
            flex: 1;

            .username {
                font-size: 30rpx;
                color: #333;
                font-weight: bold;
            }

            .date-location {
                font-size: 24rpx;
                color: #999;
            }
        }

        .reply {
            font-size: 24rpx;
            color: #666;
        }
    }

    .content {
        font-size: 28rpx;
        color: #333;
        line-height: 1.5;
        margin-top: 10rpx;
    }

    .reply-area {
        background-color: #eef1f5;
        border-radius: 10rpx;
        padding: 10rpx;
        margin-top: 10rpx;

        .my-reply {
            display: flex;
            align-items: center;

            .avatar {
                width: 40rpx;
                height: 40rpx;
                border-radius: 50%;
                margin-right: 10rpx;
            }

            .reply-content {
                .reply-text {
                    font-size: 24rpx;
                    color: #666;
                }

                .reply-message {
                    font-size: 28rpx;
                    color: #333;
                    line-height: 1.5;
                }
            }
        }
    }
}
</style>