<script setup lang="ts">
import { ref, onMounted } from 'vue';
import Card from "@/components/jbs/card.vue";
import type { Script } from '@/types/Script';
import { getData } from "../constVar";
const scriptList = ref<Script[]>([]);

onMounted(async () => {
    scriptList.value = await getData();
});
</script>

<template>
    <view class="wantPlay">

        <up-card :border="false" padding= 0 :show-foot="false" :border-radius="12" :head-border-bottom="false"
            margin= '10rpx' v-for="(script, index) in scriptList" :key="index">
            <template #body>
                <view class="card">
                <Card :script="script" displayMode="WANTPALY" />
                </view>
            </template>
        </up-card>

    </view>
</template>

<style scoped lang="scss">
@import "@/static/scss/variables.module.scss";

.wantPlay {
    background-color:#F1F6FF

}
.card{
    height: 100%;
    width: 100%;
}

</style>