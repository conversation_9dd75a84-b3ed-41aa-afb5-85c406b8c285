<script setup lang="ts">
// 事件通过 emit 传递给父组件
const emit = defineEmits<{
  (e: 'cancel'): void;
  (e: 'submit'): void;
}>();
</script>

<template>
  <view class="footer-buttons">
    <u-button class="btn" type="default" @click="$emit('cancel')">取消</u-button>
    <u-button class="btn" type="primary" @click="$emit('submit')">提交</u-button>
  </view>
</template>

<style scoped lang="scss">
.footer-buttons {
  display: flex;
  justify-content: space-evenly;
  padding: 30rpx 60rpx;
  background-color: white;
  border-top: 1rpx solid #eee;

  .btn {
    width: 45%;
    font-size: 28rpx;
  }
}
</style>