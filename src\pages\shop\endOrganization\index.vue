<script setup lang="ts">

import OrderSummaryCard from './orderSummaryCard.vue';
import SatisfactionRatingCard from './satisfactionRatingCard.vue';
import StaffEvaluationCard from './staffEvaluationCard.vue';
import FooterButtons from './footerButtons.vue';
import { ref } from 'vue';
import profile from '@/static/images/profile.jpg'
const overallAnonymous = ref(false);
const staffList = ref([
    {
        name: 'Dm 小鹿',
        serviceTime: '04小时55分钟',
        avatar: profile,
        isAnonymous: false,
    },
    {
        name: 'Dm 阿星',
        serviceTime: '03小时20分钟',
        avatar: profile,
        isAnonymous: false,
    },
]);
const evaluationData = ref({
    ratings: { overall: 0, plot: 0, logic: 0 },
    comment: '',
    isAnonymous: false,
});

const ratingItems = [
    {
        label: '总体',
        key: 'overall',
        description: ['非常差', '一般', '超赞'],
    },
];

const cancelEvaluation = () => {
    uni.showToast({ title: '已取消', icon: 'none' });
};

const handleUpdateStaff = (val: typeof staffList.value) => {
    staffList.value = val;
};

const handleUpdateOverallAnonymous = (val: boolean) => {
    overallAnonymous.value = val;
};

// 数据
const order = {
    productName: '崩塌',
    orderId: '31245895412365778',
    amount: 88,
    discount: '12',
};

const scriptTitle = '崩·塌';
const alertMessage = '提交评价或建议，可以鼓励店家与Dm做的更好哦~'


// 事件处理
const goBack = () => {
    uni.navigateBack();
};
const selectedTags = ref<string[]>([]);
const submitReview = () => {
    console.log('选中的标签:', selectedTags.value);
    uni.showToast({ title: '提交成功', icon: 'success' });
};
</script>

<template>
    <view class="page-container">
        <view class="top">
            <up-alert :title="alertMessage" type="warning" />
        </view>

        <OrderSummaryCard :order="order" />
        <SatisfactionRatingCard :title="scriptTitle" v-model="evaluationData" />
        <StaffEvaluationCard :rating-items="ratingItems" :staff-members="staffList"
            :overall-anonymous="overallAnonymous" @cancel="cancelEvaluation" @update:staff="handleUpdateStaff"
            @update:overall-anonymous="handleUpdateOverallAnonymous"
            @update:selectedTags="(tags) => selectedTags = tags" />
        <FooterButtons @cancel="goBack" @submit="submitReview" />
    </view>
</template>

<style scoped lang="scss">
@import "@/static/scss/variables.module.scss";

.page-container {
    min-height: 100vh;
    background-color: $background-color;
    display: flex;
    flex-direction: column;

    .top {

        padding: 10px 15px;

    }
}
</style>