<script setup lang="ts">
import { defineProps } from 'vue';

interface Order {
    productName: string;
    orderId: string;
    amount: number;
    discount: string;
}

defineProps<{
    order: Order;
}>();
</script>

<template>
    <view class="order-summary-card">
        <up-card :border="false" padding=15 :show-foot="false" :border-radius="12" :head-border-bottom="false"
            head-style="padding:0" margin="20rpx">
            <template #body>
                <view class="content">
                    <view class="card-header">
                        <text>商品名称：{{ order.productName }}</text>
                        <text class="detail-link">订单详情 ></text>
                    </view>
                    <view class="order-info">
                        <text>订单号：{{ order.orderId }}</text>

                    </view>
                    <view class="bottom">
                        <text class="paymentAmount">付款金额:</text>
                        <view class="price"><up-icon name="rmb" color="black" size="14" bold="true"></up-icon><text
                                class="number">{{ order.amount }}</text>
                        </view>
                        <text>已优惠 {{ order.discount }}</text>
                    </view>
                </view>
            </template>
        </up-card>
    </view>
</template>

<style scoped lang="scss">
@import "@/static/scss/variables.module.scss";

.order-summary-card {



    .content {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        gap: 15rpx;
    }

    .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 20rpx;
        color: $primary-text-color;

    }

    .order-info {
        display: flex;
        flex-direction: column;
        gap: 8rpx;
        font-size: 20rpx;
        color: $secondary-text-color;
    }

    .bottom {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        font-size: 20rpx;
        line-height: 30rpx;
        gap: 5rpx;

        .paymentAmount {
            color: $primary-text-color;
        }

        .price {
            display: flex;
            justify-content: space-between;

            font-weight: bold;
        }

        .number {
            color: black;
            font-size: 28rpx;
        }
    }


}
</style>