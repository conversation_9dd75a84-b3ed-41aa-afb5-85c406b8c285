<script setup lang="ts">
import { defineProps, computed, ref, watch } from 'vue';

interface RatingItem {
    label: string;
    key: string;
    description: string[];
}

interface Props {
    title: string;
    ratingItems?: RatingItem[];
    modelValue?: {
        ratings: Record<string, number>;
        comment: string;
        isAnonymous: boolean;
    };
}
const props = defineProps<Props>();
const emit = defineEmits<{
    (e: 'update:modelValue', value: {
        ratings: Record<string, number>;
        comment: string;
        isAnonymous: boolean;
    }): void;
}>();

// 默认评分项
const defaultRatingItems: RatingItem[] = [
    { label: '总体', key: 'overall', description: ['非常差', '差', '一般', '满意', '非常满意'] },
    { label: '剧情', key: 'plot', description: ['非常差', '差', '一般', '满意', '非常满意'] },
    { label: '逻辑', key: 'logic', description: ['非常差', '差', '一般', '满意', '非常满意'] },
];

// 提供默认值
const finalRatingItems = computed(() => props.ratingItems || defaultRatingItems);

// 响应式数据
const ratings = ref<Record<string, number>>({ ...props.modelValue?.ratings });
const comment = ref<string>(props.modelValue?.comment || '');
const isAnonymous = ref<boolean>(props.modelValue?.isAnonymous || false);

// 初始化：默认 0（表示未评分）
finalRatingItems.value.forEach(item => {
    if (ratings.value[item.key] === undefined) {
        ratings.value[item.key] = 0;
    }
});
// 监听变化并同步
watch(
    () => [ratings.value, comment.value, isAnonymous.value],
    () => {
        emit('update:modelValue', {
            ratings: { ...ratings.value },
            comment: comment.value,
            isAnonymous: isAnonymous.value,
        });
    },
    { deep: true }
);
</script>

<template>
    <view class="satisfaction-rating-card">
        <up-card :border="false" padding=15 :show-foot="false" :border-radius="12" :head-border-bottom="false"
            head-style="padding:0" margin="20rpx">
            <template #body>

                <!-- 标题 + 匿名 -->
                <view class="card-header">
                    <view class="left">
                        <text class="title">您对此本满意吗 </text>
                        <text class="name">>{{ title }}</text>
                    </view>
                    <up-checkbox-group placement="column">
                        <u-checkbox v-model="isAnonymous" label="匿名评价" size="14" shape="circle" labelColor="black"
                            inactiveColor="#CDDFFF" />
                    </up-checkbox-group>

                </view>

                <!-- 评分项列表 -->
                <view class="rating-section">
                    <view v-for="item in finalRatingItems" :key="item.key" class="rating-item">
                        <!-- 标签 -->
                        <text class="label">{{ item.label }}：</text>

                        <!-- 使用 up-rate 组件 -->
                        <up-rate v-model="ratings[item.key]" :count="5" :size="22" activeColor="#FDCB6D" gutter="30"
                            inactiveColor="#FEEBC7" />

                        <!-- 只有评分 > 0 时才显示描述 -->
                        <text v-if="ratings[item.key] > 0" class="desc">
                            {{ item.description[ratings[item.key] - 1] }}
                        </text>

                    </view>
                </view>

                <!-- 评论输入 -->
                <view class="comment-section">
                    <up-textarea class="comment-input" maxlength="200"
                        placeholder="请客观的进行评价（可以从剧情、手法、有无bug、有无酱油，是否吃玩家类型等方面进行评价）" v-model="comment"></up-textarea>
                </view>
            </template>
        </up-card>
    </view>
</template>

<style scoped lang="scss">
@import "@/static/scss/variables.module.scss";

.satisfaction-rating-card {


    .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        color: $primary-text-color;
        gap: 5rpx;

        .title {
            font-size: 30rpx;
            font-weight: bold;
        }

        .name {
            color: $secondary-text-color;
            font-size: 24rpx;
        }
    }

    .rating-section {
        display: flex;
        flex-direction: column;
        gap: 16rpx;

        .rating-item {
            display: grid;
            grid-template-columns: auto 1fr auto;
            font-size: 26rpx;
            color: $secondary-text-color;
            gap: 20rpx; // 添加列间距

            .label {

                color: black;
            }

            .desc {
           
                min-width: 120rpx; // 设置最小宽度，根据实际情况调整
                color: #999;

            }
        }
    }

    .comment-section {
        margin-top: 16rpx;

        .comment-input {
            width: 100%;
            border-radius: $border-radius-small;
            padding: 12rpx;
            font-size: 24rpx;
            background-color: $background-color
        }
    }
}
</style>