<script setup lang="ts">
import { defineProps, defineEmits, withDefaults, ref } from 'vue';

// 定义店员信息类型
interface StaffMember {
    name: string;
    serviceTime: string;
    avatar: string; // 头像 URL
    isAnonymous: boolean;
}

// 定义评分项类型
interface RatingItem {
    label: string;
    key: string;
    description: string[];
}

// 定义 Props
interface Props {
    ratingItems: RatingItem[];
    staffMembers: StaffMember[];
    overallAnonymous: boolean; // 整体是否匿名（顶部开关）
}

// 使用 withDefaults 提供默认值（避免父组件未传时出错）
const props = withDefaults(defineProps<Props>(), {
    ratingItems: () => [
        {
            label: '总体',
            key: 'overall',
            description: ['非常差', '一般', '超赞'],
        },
    ],
    staffMembers: () => [],
    overallAnonymous: false,
});

// 定义事件
const emit = defineEmits<{
    (e: 'cancel'): void;
    (e: 'update:overallAnonymous', value: boolean): void;
    (e: 'update:staff', value: StaffMember[]): void;
    (e: 'update:selectedTags', value: string[]): void;
}>();

// 响应式评分数据
const ratings = ref<Record<string, number>>({});
props.ratingItems.forEach(item => {
    ratings.value[item.key] = 0;
});

// 可选标签列表
const availableTags = ref([
    '特别帅',
    '贴心暖男',
    '幽默风趣',
    '服务周到',
    '帮拿外卖',
    '活跃气氛',
    '演技炸裂',
    '脱稿复盘',
    '节奏恰好，扶车得当',
    '全程在线，不玩手机'
]);

// 用户选择的标签
const selectedTags = ref<string[]>([]);

// 处理整体匿名变化
const handleAnonymousChange = (val: boolean) => {
    emit('update:overallAnonymous', val);
};

// 同步子店员的匿名状态变化
const handleStaffAnonymousChange = (index: number, val: boolean) => {
    const updated = [...props.staffMembers];
    updated[index].isAnonymous = val;
    emit('update:staff', updated);
};

// 处理标签选择
const handleTagSelect = (tag: string) => {
    console.log('Tag clicked:', tag); // 🔥 调试：确认是否触发
    if (selectedTags.value.includes(tag)) {
        selectedTags.value = selectedTags.value.filter(t => t !== tag);
    } else {
        selectedTags.value.push(tag);
    }
    console.log('Selected tags:', selectedTags.value); // 查看数组是否更新
    emit('update:selectedTags', selectedTags.value);
};
</script>

<template>
    <view class="staff-evaluation-card">
        <!-- 顶部标题 + 匿名开关 -->
        <up-card :border="false" padding=15 :show-foot="false" :border-radius="12" :head-border-bottom="false"
            head-style="padding:0" margin="20rpx">
            <template #body>
                <view class="card-header">
                    <text class="title">您对本场店员满意吗</text>
                    <up-checkbox-group placement="column">
                        <u-checkbox :model-value="overallAnonymous" @change="handleAnonymousChange" label="匿名评价"
                            size="14" shape="circle" labelColor="black" inactiveColor="#CDDFFF" />
                    </up-checkbox-group>

                </view>

                <!-- 总体评分 -->
                <view class="rating-section">
                    <view v-for="item in ratingItems" :key="item.key" class="rating-item">
                        <text class="label">{{ item.label }}：</text>
                        <up-rate v-model="ratings[item.key]" :count="3" :size="22" activeColor="#FDCB6D" gutter="30"
                            inactiveColor="#FEEBC7" />
                        <text v-if="ratings[item.key] > 0" class="desc">
                            {{ item.description[ratings[item.key] - 1] }}
                        </text>
                    </view>
                </view>

                <!-- 标签选择 -->
                <view class="tag-list">
                    <view v-for="(tag, index) in availableTags" :key="index" class="tag-item">
                        <up-tag :text="tag" type="info" size="mini" :plain="!selectedTags.includes(tag)"
                            :bgColor="selectedTags.includes(tag) ? '#f1f6ff' : '#5C92E9'"
                            :color="selectedTags.includes(tag) ? 'black' : 'white'" :name="index"
                             borderColor="transparent"
                            @click="handleTagSelect(tag)" />
                    </view>
                </view>

                <!-- 多个店员评价 -->
                <view v-if="staffMembers.length > 0" class="staff-members-section">
                    <view class="top">
                        <text class="section-title">评价多个店员 ({{ staffMembers.length }}):</text>
                        <view class="right">
                            <text class="cancel-btn" @click="$emit('cancel')">取消</text>
                            <up-icon name="close" size="25"></up-icon>
                        </view>
                    </view>

                    <view v-for="(member, index) in staffMembers" :key="member.name" class="staff-member">
                        <img :src="member.avatar" class="staff-avatar" />
                        <view class="evaluationInformation">
                            <view class="userInfo">
                                <text class="staff-name">{{ member.name }}</text>

                                <up-checkbox-group placement="column">
                                    <u-checkbox :model-value="member.isAnonymous"
                                        @change="handleStaffAnonymousChange(index, $event)" label="匿名评价" size="14"
                                        shape="circle" labelColor="black" inactiveColor="#CDDFFF" />
                                </up-checkbox-group>
                            </view>
                            <text class="service-time">服务时长: {{ member.serviceTime }}</text>
                        </view>
                    </view>
                </view>
            </template>
        </up-card>
    </view>
</template>

<style scoped lang="scss">
@import "@/static/scss/variables.module.scss";

.staff-evaluation-card {

    .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 28rpx;
        color: $primary-text-color;

        .title {
            font-size: 30rpx;
            font-weight: bold;
        }

    }

    .rating-section {
        display: flex;
        flex-direction: column;
        gap: 30rpx;

        .rating-item {
            display: grid;
            // 定义三列：标签、星星评分、描述文本
            grid-template-columns: minmax(80rpx, auto) minmax(150rpx, 1fr) minmax(80rpx, auto); // 设置每列的最小宽度和最大宽度
            gap: 20rpx; // 增加列间距


            .label {
                color: black;
                padding-right: 50rpx; // 增加标签与星星评分之间的间距
            }

            .desc {
                min-width: 120rpx; // 设置最小宽度，根据实际情况调整
                color: #999;

            }
        }
    }

    .tag-list {
        display: flex;
        flex-wrap: wrap;
        gap: 12rpx;
        margin-top: 16rpx;

        .tag-item {

            // 点击反馈
            &:active {
                opacity: 0.7;
            }
        }
    }

    .staff-members-section {
        margin-top: 20rpx;

        .top {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .section-title {
            flex: 1;
            font-size: 26rpx;
            color: $primary-text-color;
            margin-bottom: 10rpx;
        }

        .right {
            display: flex;
            align-items: center;
            height: 20rpx;
            line-height: 20rpx;
            gap: 5rpx;

            .cancel-btn {
                font-size: 24rpx;
                color: #666;
            }
        }



        .staff-member {
            display: flex;
            align-items: center;
            gap: 16rpx;
            padding: 16rpx 0;
            border-bottom: 1rpx solid #eee;
            justify-content: flex-start;

            .staff-avatar {
                width: 10%;
                border-radius: 50%;
                object-fit: cover;
            }

            .evaluationInformation {
                flex: 1;
                display: flex;
                flex-direction: column;
                gap: 8rpx;
            }

            .userInfo {
                display: flex;
                justify-content: space-between;
                align-items: center;
            }

            .staff-name {
                font-size: 26rpx;
                color: $primary-text-color;
                flex: 1;
            }

            .service-time {
                font-size: 22rpx;
                color: #666;
            }
        }
    }
}
</style>