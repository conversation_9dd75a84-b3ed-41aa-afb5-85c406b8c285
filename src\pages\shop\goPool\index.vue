<script setup>
import xuexiang from "@/static/images/cover/xuexiang.jpg";
import { ref, computed } from "vue";
import ManFill from "@/static/images/icon/manFill.png";
import WomanFill from "@/static/images/icon/womanFill.png";
const scriptList =
{
    id: 5,
    title: '雪乡连环杀人事件',
    cover: xuexiang,
    tags: ['推理', '惊悚', '现代', '本格'],
    players: '3男3女',
    duration: '5小时',
    difficulty: '适中',
    address: '山西省太原市茂业一期光影剧本杀',
    distance: '10.0km',
    avatars: [
        'https://uview-plus.jiangruyi.com/uview-plus/album/1.jpg',
        'https://uview-plus.jiangruyi.com/uview-plus/album/2.jpg',
        'https://uview-plus.jiangruyi.com/uview-plus/album/3.jpg',
        'https://uview-plus.jiangruyi.com/uview-plus/album/4.jpg',
        'https://uview-plus.jiangruyi.com/uview-plus/album/7.jpg',
        'https://uview-plus.jiangruyi.com/uview-plus/album/6.jpg',
        'https://uview-plus.jiangruyi.com/uview-plus/album/5.jpg'
    ],
    waitingNumber: '2',
    startTime: '05/14 周三 14:00',
    rating: 3,
    heat: 3,
    score: 9.2,
    alreadyNumber: 6,
    totalNumber: 8,
    price: 98,
    introduction: '年三十，北道河，村里出了个杀人魔。杀了一个又一个，最后一个杀老婆。七个小孩儿来串门，联起手来把案破。年三十，北道河，村里出了个杀人魔。杀了一个又一个，最后一个杀老婆。七个小孩儿来串门，联起手来把案破。年三十，北道河，村里出了个杀人魔。杀了一个又一个，最后一个杀老婆。七个小孩儿来串门，联起手来把案破。年三十，北道河，村里出了个杀人魔。杀了一个又一个，最后一个杀老婆。七个小孩儿来串门，联起手来把案破。年三十，北道河，村里出了个杀人魔。杀了一个又一个，最后一个杀老婆。七个小孩儿来串门，联起手来把案破。年三十，北道河，村里出了个杀人魔。杀了一个又一个，最后一个杀老婆。七个小孩儿来串门，联起手来把案破。年三十，北道河，村里出了个杀人魔。杀了一个又一个，最后一个杀老婆。七个小孩儿来串门，联起手来把案破。',
    paymentStatus: 2,
    wantPlayStatus: 1,
    role: [
        {
            id: 1,
            name: '量子一',
            cover: xuexiang,
        },
        {
            id: 2,
            name: '椅子两',
            cover: xuexiang,
        },
        {
            id: 2,
            name: '椅子两',
            cover: xuexiang,
        },
        {
            id: 2,
            name: '椅子两',
            cover: xuexiang,
        },
    ],
    paymentStatus: 0,
}
const goToPay = (id) => {
    uni.navigateTo({
        url: `/pages/shop/payment/index?id=${id}`
    });
};
const storeData = ref({
    id: 1,
    name: '啊哈哈',
    signature: '啊哈哈哈哈哈',
    cover: xuexiang,
    sex: '男'
});

const nav = ref("剧本详情")
const navs = ref(['剧本详情', '剧本评价', '拼场须知']);
const current = computed({
    get: () => {
        return navs.value.indexOf(nav.value);
    },
    set: (value) => {
        nav.value = navs.value[value];
    }
});
function goToGHome() {
    uni.switchTab({
        url: `/pages/home/<USER>
    });
}
const show = ref(true);
const agreePrivacy = ref(false);
</script>
<template>
    <view class="app-container">
        <view class="top">
            <up-icon name="arrow-left" @click="goToGHome()" label="你好"></up-icon>
        </view>

        <view class="container-head">
            <view class="container-head-image">
                <up-image :src="scriptList.cover" width="200rpx" height="280rpx" radius="20rpx" />
            </view>
            <view class="container-head-right">
                <text class="title">{{ scriptList.title }}</text>
                <view class="action-heat">
                    <view class="script-kill-heat">热度:</view>
                    <up-rate :count="5" v-model="scriptList.heat" class="script-kill-rate" :gutter="2" :size="10"
                        :activeColor="'#ffa500'" readonly />
                </view>
                <view class="price-text">
                    <view class="script-kill-text">
                        {{ scriptList.players }} · {{ scriptList.duration }} · {{ scriptList.difficulty }}
                    </view>
                    <view class="text-price">
                        {{ scriptList.price }} /人
                    </view>
                </view>
                <up-tag v-for="(item, index) in scriptList.tags" :text="item" :key="index" plain plainFill
                    borderColor="transparent" bgColor="#ffffff" class="tag-item" size="mini" color="#867676" />
                <view class="brief-introduction">
                    <up-icon name="order" color="#409EFF" size="18"></up-icon>
                    <text class="introduction-text">{{ scriptList.introduction }}</text>
                </view>
            </view>
        </view>
        <view class="middle">
            <view class="middle-one">
                <view class="start-time">
                    <view class="start-time-icon">
                        <up-icon name="clock-fill" color="#2979ff" size="23" />
                        <view class="start-time-text">开始时间</view>
                    </view>
                    <view class="start-text">{{ scriptList.startTime }}</view>
                </view>
                <view class="drive-number">
                    <view class="drive-number-icon">
                        <up-icon name="account-fill" color="#2979ff" size="23" />
                        <view class="drive-number-text">开车人数</view>
                    </view>
                    <view class="drive-user-number">
                        <text>{{ scriptList.totalNumber }}人 已有</text>
                        <text class="drive-text">{{ scriptList.alreadyNumber }}</text>
                        <text>人 | 等{{ scriptList.totalNumber - scriptList.alreadyNumber }}人</text>
                    </view>
                </view>
            </view>
            <view class="business-information">
                <view class="business-image">
                    <up-image :src="scriptList.cover" width="80rpx" height="80rpx" radius="20rpx" shape="circle" />
                </view>
                <view class="business-text">
                    <view class="business-title">{{ scriptList.title }}</view>
                    <view class="address-distance">
                        <up-icon name="map-fill" color="#2979ff" size="16" />
                        <text class="script-kill-address">{{ scriptList.address }}</text>
                        <text class="script-kill-address"> · </text>
                        <text class="script-kill-distance">{{ scriptList.distance }}</text>
                    </view>
                </view>
            </view>
        </view>
        <view class="container-middle">
            <view class="carpool-members">
                <text class="carpool-members-text">拼车成员 </text>
                <text class="already-number"> 5 </text>
                <text class="carpool-members-text">/8</text>
            </view>
            <view class="avatar-name-list" v-for="i in 5">
                <view class="avatar-name">
                    <view class="avatar-name-image">
                        <up-avatar :src="storeData.cover" size="50"></up-avatar>
                        <up-image :src="ManFill" width="30rpx" height="30rpx" v-if="storeData.sex == '男'"
                            class="script-kill-DM-sex" />
                    </view>
                    <view class="user-name-signature">
                        <view class="user-name">{{ storeData.name }}</view>
                        <view class="user-signature">{{ storeData.signature }}</view>
                    </view>
                </view>
            </view>
        </view>
        <up-popup :show="show" :round="10" mode="bottom" :overlay="false" customStyle="height: 150rpx;">
            <view class="popup-content">
                <view class="agreement-button">
                    <view class="popup-text">组局成功后不可退款</view>
                    <view class="privacy-agreement">
                        <checkbox class="privacy-checkbox" :checked="agreePrivacy"
                            @click="agreePrivacy = !agreePrivacy" />
                        <text class="privacy-text">我已阅读并同意</text>
                        <text class="text-blue">《隐私协议》</text>
                    </view>
                </view>
                <view class="button-group">
                    <up-button v-if="scriptList.paymentStatus === 0" type="primary" class="script-kill-button"
                        @click="goToPay(scriptList.id)">去支付</up-button>
                    <up-button v-if="scriptList.paymentStatus === 1" type="primary" class="script-kill-button">已支付
                        ¥{{ scriptList.price }} 退出组局</up-button>
                </view>
            </view>
        </up-popup>
    </view>
</template>
<style lang="scss" scoped>
@import "@/static/scss/leyout.scss";

.app-container {
    padding: $sp-page;

    .top {
        padding: 15rpx 0;
    }

    .container-head {
        display: flex;
        align-items: center;
        gap: 20rpx;

        .container-head-right {
            height: 300rpx;

            .title {
                font-weight: 600;
            }

            .action-heat {
                margin-top: 20rpx;
                display: flex;
                padding: 2rpx 20rpx;
                background-color: #ffffff;
                border: 1px solid #ffffff;
                border-radius: 80rpx;
                width: 200rpx;


                .script-kill-heat {
                    font-size: 18rpx;
                    margin-right: 10rpx;
                }
            }

            .price-text {
                display: flex;
                justify-content: space-between;
                align-items: center;

                .script-kill-text {
                    font-size: 26rpx;
                    color: #000000;
                    margin-top: 20rpx;
                }

                .text-price {
                    font-size: 26rpx;
                    color: #ff0000;
                    font-weight: 600;
                }
            }

            .tag-item {
                margin-top: 20rpx;
                margin-right: 10rpx;
            }



            .brief-introduction {
                display: flex;
                align-items: flex-start;
                background: #ffffff;
                border-radius: 4rpx;
                margin-top: 20rpx;

                .introduction-text {
                    display: -webkit-box;
                    line-clamp: 2;
                    -webkit-line-clamp: 2;
                    -webkit-box-orient: vertical;
                    font-size: 20rpx;
                    overflow: hidden;
                    text-overflow: ellipsis;
                }
            }
        }
    }

    .middle {
        .middle-one {
            margin-top: 20rpx;
            display: flex;
            gap: 10rpx;

            .start-time {
                flex: 1;
                background: #ffffff;
                padding: 20rpx;

                .start-time-icon {
                    display: flex;
                }

                .start-text {
                    font-weight: 600;
                }
            }

            .drive-number {
                flex: 1;
                background: #ffffff;
                padding: 20rpx;

                .drive-number-icon {
                    display: flex;
                }

                .drive-user-number {
                    font-weight: 600;
                }

                .drive-text {
                    color: #4bb4ff;
                }
            }
        }
    }

    .business-information {
        display: flex;
        margin-top: 20rpx;
        background: #ffffff;
        padding: 20rpx;
        gap: 20rpx;

        .business-image {}

        .business-text {
            display: flex;
            flex-direction: column;
            justify-content: space-between;

            .business-title {
                font-size: 28rpx;
                font-weight: 600;
            }

            .address-distance {
                display: flex;
                justify-content: space-between;
                align-items: center;

                text {
                    display: block;
                }

                .script-kill-address {
                    color: #999999;
                    font-size: 22rpx;
                }

                .script-kill-distance {
                    color: #999999;
                    font-size: 24rpx;
                }
            }
        }
    }

    .container-middle {
        margin-top: 20rpx;

        .carpool-members {
            display: flex;
            align-items: baseline;
            margin-bottom: 20rpx;

            .carpool-members-text {
                font-weight: 500;
            }

            .already-number {
                margin-left: 10rpx;
                color: #4bb4ff;
                font-weight: 500;
            }
        }

        .avatar-name-list {
            background: #ffffff;
            padding: 20rpx;

            .avatar-name {
                display: flex;
                gap: 20rpx;

                .avatar-name-image {
                    display: flex;
                    align-items: flex-end;

                    .script-kill-DM-sex {
                        margin-left: -25rpx;
                    }
                }

                .user-name-signature {
                    display: flex;
                    flex-direction: column;
                    gap: 10rpx;

                    .user-name {
                        font-size: 32rpx;
                        font-weight: 500;
                        color: #333333;
                    }

                    .user-signature {
                        font-size: 20rpx;
                        color: #c4c4c4;
                    }
                }
            }
        }
    }

    .popup-content {
        padding: 40rpx;
        display: flex;
        justify-content: space-between;
        align-items: flex-end;

        .agreement-button {

            .popup-text {
                font-size: 26rpx;
                font-weight: 600;
            }

            .privacy-agreement {

                .privacy-checkbox {
                    transform: scale(0.5);
                }

                .privacy-text {
                    font-size: 20rpx;
                }

                .text-blue {
                    color: #4bb4ff;
                    font-size: 22rpx;
                }
            }

        }

        .button-group {
            .script-kill-button {
                border-radius: 40rpx;
            }
        }
    }
}
</style>