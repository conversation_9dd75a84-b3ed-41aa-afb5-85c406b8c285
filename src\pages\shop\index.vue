<script setup lang="ts">
import NavSelect from '@/components/jbs/nav-select.vue';
import TagSelect from '@/components/jbs/tag-select.vue';
import { computed, ref } from 'vue';
import xuexiang from "@/static/images/cover/xuexiang.jpg";
import ShopCard from './shopCard.vue';
const keyword = ref('');
const storeData = ref({
    id: 1,
    name: '谋杀之谜秘书旗舰店',
    rating: 5,
    price: '99',
    distance: '10.0',
    currentPlayers: '2',
    review: '非常好非常好，评价评价评价评价..',
    cover: xuexiang
});
const nav = ref("推荐")
const navs = ref(['桌面剧本', 'AI剧本']);
const current = computed({
    get: () => {
        return navs.value.indexOf(nav.value);
    },
    set: (value) => {
        nav.value = navs.value[value];
    }
});
const goToStore = (storeId: number) => {
    uni.navigateTo({
        url: `/pages/shop/stores/index?storeId=${storeId}`
    });
};
const goToScreenPlay = (screenPlayId: number) => {
    uni.navigateTo({
        url: `/pages/shop/screenPlay/index?screenPlayId=${screenPlayId}`
    });
};


</script>
<template>
    <view class="app-container">
        <view class="search-container ppd-t ppd-l">
            <up-search placeholder="搜索" v-model="keyword" :showAction="false" bgColor="#e8f2ff" />
            <NavSelect :nav="navs" v-model="nav" flex="start" />
            <view class="tag-select-container">
                <text>附近</text>
                <up-icon name="arrow-down-fill" size="12" />
                <TagSelect />
            </view>
        </view>
        <swiper class="swiper" :duration="500" style="height: 85vh;" :current="current"
            @change="current = $event.detail.current">
            <swiper-item>
                <view class="content">
                    <view v-for="i in 10">
                        <ShopCard :store="storeData" @click="goToStore(storeData.id)"></ShopCard>
                    </view>
                </view>
            </swiper-item>
            <swiper-item>
                <view class="content">
                    <view v-for="i in 10">
                        <ShopCard :store="storeData" @click="goToScreenPlay(storeData.id)"></ShopCard>
                    </view>
                </view>
            </swiper-item>
        </swiper>
    </view>
</template>
<style scoped lang="scss">
@import "@/static/scss/leyout.scss";
@import "@/static/scss/variables.module.scss";

.app-container {
    background-color: #ffffff;
}

.search-container {
    background-color: #ffffff;
    width: 100%;
    padding-bottom: $sp-small;
    position: sticky;
    top: 0;
    z-index: 999;

    .u-search {
        width: 500rpx;
    }



    .tag-select-container {
        display: flex;
        align-items: center;

        text {
            width: 100rpx;
        }

        .u-icon {
            margin-right: $sp-card;
        }
    }
}

.content {
    height: 100%;
    padding: 0 $sp-page;
    display: flex;
    flex-direction: column;
    gap: $sp-card;
    overflow-y: auto;
}
</style>