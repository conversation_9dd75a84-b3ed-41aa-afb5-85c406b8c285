<script setup>
import { ref } from 'vue'
import xuexiang from "@/static/images/cover/xuexiang.jpg";
const current = ref(0)
const items = ['拼场预定', '包场预定']
const scriptList =
{
    id: 5,
    title: '雪乡连环杀人事件',
    cover: xuexiang,
    shop: xuexiang,
    tags: ['推理', '惊悚', '现代', '本格'],
    players: '3男3女',
    duration: '5小时',
    difficulty: '适中',
    address: '山西省太原市茂业一期光影剧本杀',
    distance: '10.0km',
    avatars: [
        'https://uview-plus.jiangruyi.com/uview-plus/album/1.jpg',
        'https://uview-plus.jiangruyi.com/uview-plus/album/2.jpg',
        'https://uview-plus.jiangruyi.com/uview-plus/album/3.jpg',
        'https://uview-plus.jiangruyi.com/uview-plus/album/4.jpg',
        'https://uview-plus.jiangruyi.com/uview-plus/album/7.jpg',
        'https://uview-plus.jiangruyi.com/uview-plus/album/6.jpg',
        'https://uview-plus.jiangruyi.com/uview-plus/album/5.jpg'
    ],
    waitingNumber: '2',
    startTime: '05/14 周三 14:00',
    rating: 3,
    heat: 3,
    score: 9.2,
    alreadyNumber: 6,
    totalNumber: 8,
    price: 98,
    introduction: '年三十，北道河，村里出了个杀人魔。杀了一个又一个，最后一个杀老婆。七个小孩儿来串门，联起手来把案破。年三十，北道河，村里出了个杀人魔。杀了一个又一个，最后一个杀老婆。七个小孩儿来串门，联起手来把案破。年三十，北道河，村里出了个杀人魔。杀了一个又一个，最后一个杀老婆。七个小孩儿来串门，联起手来把案破。年三十，北道河，村里出了个杀人魔。杀了一个又一个，最后一个杀老婆。七个小孩儿来串门，联起手来把案破。年三十，北道河，村里出了个杀人魔。杀了一个又一个，最后一个杀老婆。七个小孩儿来串门，联起手来把案破。年三十，北道河，村里出了个杀人魔。杀了一个又一个，最后一个杀老婆。七个小孩儿来串门，联起手来把案破。年三十，北道河，村里出了个杀人魔。杀了一个又一个，最后一个杀老婆。七个小孩儿来串门，联起手来把案破。',
    paymentStatus: 2,
    wantPlayStatus: 1,
    role: [
        {
            id: 1,
            name: '量子一',
            cover: xuexiang,
        },
        {
            id: 2,
            name: '椅子两',
            cover: xuexiang,
        },
        {
            id: 2,
            name: '椅子两',
            cover: xuexiang,
        },
        {
            id: 2,
            name: '椅子两',
            cover: xuexiang,
        },
    ],
    paymentStatus: 0,
}
const onClickItem = (e) => {
    if (current.value !== e.currentIndex) {
        current.value = e.currentIndex
    }
}

const value = ref(true)
const goEndOrganization = () => {
    uni.navigateTo({
        url: `/pages/shop/endOrganization/index`
    });
}
</script>

<template>
    <view class="app-container">
        <view class="main-content">
            <uni-section class="app-section">
                <view class="uni-padding-wrap uni-common-mt">
                    <uni-segmented-control :current="current" :values="items" @clickItem="onClickItem" />
                </view>
                <view class="content">
                    <view v-if="current === 0">
                        <view class="business-information">
                            <view class="business-image">
                                <up-image :src="scriptList.shop" width="130rpx" height="130rpx" radius="20rpx"
                                    shape="square" />
                            </view>
                            <view class="business-text">
                                <view class="business-title">{{ scriptList.title }}</view>
                                <view class="address-distance">
                                    <up-icon name="map-fill" color="#2979ff" size="16" />
                                    <text class="script-kill-address">{{ scriptList.address }}</text>
                                </view>
                                <view class="script-kill-info">
                                    <text class="balance">本店余额:¥{{ scriptList.price }}</text>
                                    <text> | </text>
                                    <text>注册会员</text>
                                    <text> | </text>
                                    <text>暂无优惠</text>
                                </view>
                            </view>
                        </view>
                        <up-line></up-line>
                        <view class="commodity-card">
                            <view class="commodity-image">
                                <up-image :src="scriptList.cover" width="130rpx" height="160rpx" radius="20rpx"
                                    shape="square" />
                            </view>
                            <view class="commodity-right">
                                <view class="commodity-title-text">{{ scriptList.title }}</view>
                                <view class="commodity-time">
                                    <text>2023.02.28 (今天)</text>
                                    <text>14:00-18:30</text>
                                    <text> | 4小时30分钟</text>
                                </view>
                                <view class="commodity-price">
                                    <text class="price-text">¥</text>
                                    <text class="price-text">{{ scriptList.price }}</text>
                                    <text class="price-text">/人</text>
                                </view>
                            </view>
                        </view>
                        <view class="venue-number">
                            <text class="venue-title">拼成人数</text>
                            <up-number-box v-model="value" bgColor="#f1f6ff"></up-number-box>
                        </view>
                        <view class="order-rules">
                            <view class="order-rule-text">·1人起拼，还差4人可开场，最多可订4人；</view>
                            <view class="order-rule-text">·多人同行建议玩家统一下单，否则满7人开场后，其他玩家不可预订；</view>
                            <view class="order-rule-text">·该场次未拼成前，着其他玩家选择包场预定，则拼场未成功，系统将自动退款。</view>
                        </view>
                    </view>
                    <view v-if="current === 1">
                        <view class="business-information">
                            <view class="business-image">
                                <up-image :src="scriptList.shop" width="130rpx" height="130rpx" radius="20rpx"
                                    shape="square" />
                            </view>
                            <view class="business-text">
                                <view class="business-title">{{ scriptList.title }}</view>
                                <view class="address-distance">
                                    <up-icon name="map-fill" color="#2979ff" size="16" />
                                    <text class="script-kill-address">{{ scriptList.address }}</text>
                                </view>
                                <view class="script-kill-info">
                                    <text class="balance">本店余额:¥{{ scriptList.price }}</text>
                                    <text> | </text>
                                    <text>注册会员</text>
                                    <text> | </text>
                                    <text>暂无优惠</text>
                                </view>
                            </view>
                        </view>
                        <up-line></up-line>
                        <view class="commodity-card">
                            <view class="commodity-image">
                                <up-image :src="scriptList.cover" width="130rpx" height="160rpx" radius="20rpx"
                                    shape="square" />
                            </view>
                            <view class="commodity-right">
                                <view class="commodity-title-text">{{ scriptList.title }}</view>
                                <view class="commodity-time">
                                    <text>2023.02.28 (今天)</text>
                                    <text>14:00-18:30</text>
                                    <text> | 4小时30分钟</text>
                                </view>
                                <view class="commodity-price">
                                    <text class="price-text">¥</text>
                                    <text class="price-text">{{ scriptList.price }}</text>
                                    <text class="price-text">/人</text>
                                </view>
                            </view>
                        </view>
                        <view class="venue-number">
                            <text class="venue-title">拼成人数</text>
                            <up-number-box v-model="scriptList.totalNumber" bgColor="#f1f6ff"
                                :disabled="true"></up-number-box>
                        </view>
                        <view class="order-rules">
                            <view class="order-rule-text">·1人起拼，还差4人可开场，最多可订4人；</view>
                            <view class="order-rule-text">·多人同行建议玩家统一下单，否则满7人开场后，其他玩家不可预订；</view>
                            <view class="order-rule-text">·该场次未拼成前，着其他玩家选择包场预定，则拼场未成功，系统将自动退款。</view>
                        </view>
                    </view>
                </view>
            </uni-section>

            <view class="user-membership">
                <view class="user-membership-left">
                    <view class="membership-text">使用会员卡</view>
                    <up-switch v-model="value" </up-switch>
                </view>
                <view class="user-coupons">
                    <view class="coupons-text">2张可用</view>
                    <up-icon name="arrow-right"></up-icon>
                </view>
            </view>
            <view class="user-membership">
                <view class="user-membership-left">
                    <view class="membership-text">使用优惠卷</view>
                    <up-switch v-model="value" </up-switch>
                </view>
                <view class="user-coupons">
                    <view class="coupons-text">3张可用</view>
                    <up-icon name="arrow-right"></up-icon>
                </view>
            </view>

            <view class="upfront-price">
                <view class="upfront-price-head">
                    <view class="upfront-price-text">预付价格</view>
                    <view class="upfront-price-number">1人*99</view>
                </view>
                <view class="upfront-price-bottom">
                    <view class="upfront-price-total">合计：</view>
                    <view class="upfront-price-total-number">¥99</view>
                </view>
            </view>

            <view class="phone-number">
                <view class="phone-number-text">手机号码</view>
                <view class="phone-number-text">187****2012</view>
            </view>
            <view class="bottom">
                <view class="remark-text">备注</view>
                <up-textarea placeholder="你可将你的需求告知商家哦，例如：不想要真人NPC。" count :maxlength="50"></up-textarea>
            </view>
        </view>
        <view class="fixed-bottom-button" @click="goEndOrganization()">
            <up-button type="primary" class="script-kill-button" @click="goEndOrganization()">支付 ¥99</up-button>
        </view>

    </view>
</template>

<style lang="scss" scoped>
@import "@/static/scss/leyout.scss";

.uni-common-mt {
    margin-top: 30px;
}

.uni-padding-wrap {
    padding: 0px 30px;
}

.app-container {
    background-color: #f1f6ff;
    padding: $sp-page;

    .app-section {
        border-radius: 30rpx;

        .content {
            display: flex;
            justify-content: center;
            align-items: center;
            text-align: center;

            .business-information {
                display: flex;
                margin-top: 20rpx;
                background: #ffffff;
                padding: 0rpx 50rpx 30rpx 50rpx;
                gap: 20rpx;

                .business-image {}

                .business-text {
                    display: flex;
                    flex-direction: column;
                    justify-content: space-between;
                    align-items: flex-start;

                    .business-title {
                        font-size: 28rpx;
                        font-weight: 600;
                    }

                    .address-distance {
                        display: flex;
                        justify-content: flex-start;
                        align-items: center;

                        text {
                            display: block;
                        }

                        .script-kill-address {
                            color: #999999;
                            font-size: 22rpx;
                        }

                    }

                    .script-kill-info {
                        .balance {
                            color: #ff6262;
                        }
                    }
                }
            }

            .commodity-card {
                display: flex;
                margin-top: 20rpx;
                background: #ffffff;
                padding: 0rpx 50rpx 30rpx 50rpx;
                gap: 20rpx;

                .business-image {}

                .commodity-right {
                    display: flex;
                    flex-direction: column;
                    justify-content: space-between;
                    align-items: flex-start;

                    .commodity-title-text {
                        font-size: 32rpx;
                        font-weight: 600;
                    }

                    .commodity-time {
                        font-size: 19rpx;
                        color: rgb(172, 172, 172);
                    }

                    .commodity-price {
                        .price-text {
                            font-size: 32rpx;
                            font-weight: 500;
                        }
                    }
                }
            }

            .venue-number {
                display: flex;
                padding: 0rpx 50rpx 30rpx 50rpx;
                justify-content: space-between;

                .venue-title {
                    font-size: 30rpx;
                    font-weight: 600;
                }
            }

            .order-rules {
                padding: 0rpx 50rpx 30rpx 50rpx;

                .order-rule-text {
                    // justify-self: start;
                    font-size: 24rpx;
                    text-align: left;
                }
            }
        }
    }


    .user-membership {
        display: flex;
        padding: 30rpx;
        margin-top: 20rpx;
        background: #ffffff;
        justify-content: space-between;
        border-radius: 30rpx;

        .user-membership-left {
            display: flex;
            gap: 20rpx;

            .membership-text {
                font-weight: 500;
            }
        }

        .user-coupons {
            display: flex;
            gap: 20rpx;

            .coupons-text {
                color: #ff6262;
                font-weight: 500;
            }
        }
    }

    .upfront-price {
        padding: 30rpx;
        margin-top: 20rpx;
        background: #ffffff;
        border-radius: 30rpx;

        .upfront-price-head {
            display: flex;
            justify-content: space-between;

            .upfront-price-text {
                font-weight: 500;
            }

            .upfront-price-number {
                color: #c0c0c0;
            }
        }

        .upfront-price-bottom {
            margin-top: 10rpx;
            display: flex;
            justify-content: flex-end;
            align-items: baseline;

            .upfront-price-total {
                font-weight: 500;
            }

            .upfront-price-total-number {
                color: #ff6262;
                font-weight: 500;
            }
        }
    }

    .phone-number {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 20rpx;
        padding: 30rpx;
        background: #ffffff;
        border-radius: 30rpx;

        .phone-number-text {
            font-weight: 500;
        }
    }

    .bottom {
        margin-top: 20rpx;
        padding: 30rpx;
        background: #ffffff;
        border-radius: 30rpx;

        .remark-text {
            font-weight: 500;
            margin-bottom: 10rpx;
        }
    }

    .main-content {
        height: 100%;
        overflow-y: auto;
        padding-bottom: 140rpx; // ⭐关键：为固定按钮留出空间，防止内容被遮挡
        box-sizing: border-box;
    }

    // 固定按钮容器
    .fixed-bottom-button {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        padding: 20rpx 30rpx;
        background-color: #fff;
        box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1); // 浮层阴影
        z-index: 999; // ⭐确保层级最高
        border-top: 1rpx solid #eee;
    }

    .script-kill-button {
        background-color: #000 !important; // 黑色背景
        color: #fff !important;
        border-radius: 40rpx;
        width: 100%;
        height: 80rpx;
        font-size: 32rpx;
        font-weight: 500;
        display: flex;
        align-items: center;
        justify-content: center;
    }
}

.content-text {
    font-size: 14px;
    color: #666;
}
</style>1