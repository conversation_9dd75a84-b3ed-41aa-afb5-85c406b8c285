<script setup>
import xuexiang from "@/static/images/cover/xuexiang.jpg";
import TitleSection from "@/pages/home/<USER>";
import shopCard from "@/pages/shop/shopCard.vue";
import { ref, computed } from "vue";
import NavSelect from '@/components/jbs/nav-select.vue';
const scriptList =
{
    id: 5,
    title: '雪乡连环杀人事件',
    cover: xuexiang,
    tags: ['推理', '惊悚', '现代', '本格'],
    players: '3男3女',
    duration: '5小时',
    difficulty: '适中',
    address: '山西省太原市茂业一期光影剧本杀',
    distance: '10.0km',
    avatars: [
        'https://uview-plus.jiangruyi.com/uview-plus/album/1.jpg',
        'https://uview-plus.jiangruyi.com/uview-plus/album/2.jpg',
        'https://uview-plus.jiangruyi.com/uview-plus/album/3.jpg',
        'https://uview-plus.jiangruyi.com/uview-plus/album/4.jpg',
        'https://uview-plus.jiangruyi.com/uview-plus/album/7.jpg',
        'https://uview-plus.jiangruyi.com/uview-plus/album/6.jpg',
        'https://uview-plus.jiangruyi.com/uview-plus/album/5.jpg'
    ],
    waitingNumber: '2',
    startTime: '今天17:00',
    rating: 3,
    heat: 3,
    score: 9.2,
    alreadyNumber: 6,
    totalNumber: 8,
    price: 98,
    introduction: '年三十，北道河，村里出了个杀人魔。杀了一个又一个，最后一个杀老婆。七个小孩儿来串门，联起手来把案破。年三十，北道河，村里出了个杀人魔。杀了一个又一个，最后一个杀老婆。七个小孩儿来串门，联起手来把案破。年三十，北道河，村里出了个杀人魔。杀了一个又一个，最后一个杀老婆。七个小孩儿来串门，联起手来把案破。年三十，北道河，村里出了个杀人魔。杀了一个又一个，最后一个杀老婆。七个小孩儿来串门，联起手来把案破。年三十，北道河，村里出了个杀人魔。杀了一个又一个，最后一个杀老婆。七个小孩儿来串门，联起手来把案破。年三十，北道河，村里出了个杀人魔。杀了一个又一个，最后一个杀老婆。七个小孩儿来串门，联起手来把案破。年三十，北道河，村里出了个杀人魔。杀了一个又一个，最后一个杀老婆。七个小孩儿来串门，联起手来把案破。',
    paymentStatus: 2,
    wantPlayStatus: 1,
    role: [
        {
            id: 1,
            name: '量子一',
            cover: xuexiang,
        },
        {
            id: 2,
            name: '椅子两',
            cover: xuexiang,
        },
        {
            id: 2,
            name: '椅子两',
            cover: xuexiang,
        },
        {
            id: 2,
            name: '椅子两',
            cover: xuexiang,
        },
    ]
}

const storeData = ref({
    id: 1,
    name: '谋杀之谜秘书旗舰店',
    rating: 5,
    price: '99',
    distance: '10.0',
    currentPlayers: '2',
    review: '非常好非常好，评价评价评价评价..',
    cover: xuexiang
});

const nav = ref("剧本详情")
const navs = ref(['剧本详情', '剧本评价', '拼场须知']);
const current = computed({
    get: () => {
        return navs.value.indexOf(nav.value);
    },
    set: (value) => {
        nav.value = navs.value[value];
    }
});
</script>
<template>
    <view class="app-container">
        <view class="container-head">
            <view class="container-head-image">
                <up-image :src="scriptList.cover" width="200rpx" height="280rpx" radius="20rpx" />
            </view>
            <view class="container-head-right">
                <text class="title">{{ scriptList.title }}</text>
                <view class="action-heat">
                    <view class="script-kill-heat">热度:</view>
                    <up-rate :count="5" v-model="scriptList.heat" class="script-kill-rate" :gutter="2" :size="10"
                        :activeColor="'#ffa500'" readonly />
                </view>
                <view class="script-kill-text">
                    {{ scriptList.players }} · {{ scriptList.duration }} · {{ scriptList.difficulty }}
                </view>
                <up-tag v-for="(item, index) in scriptList.tags" :text="item" :key="index" plain plainFill
                    borderColor="transparent" bgColor="#ffffff" class="tag-item" size="mini" color="#867676" />
                <view class="brief-introduction">
                    <up-icon name="order" color="#409EFF" size="18"></up-icon>
                    <text class="introduction-text">{{ scriptList.introduction }}</text>
                </view>
            </view>
        </view>
        <view class="container-middle">
            <view class="middle-head">
                <TitleSection title="可玩商家" subTitle="（6）" butText="查看全部" />
            </view>
            <view v-for="i in 3">
                <shopCard :store="storeData"></shopCard>
            </view>
        </view>
    </view>
    <view class="card-page-top">
        <view class="nav-container">
            <NavSelect :nav="navs" v-model="nav" flex="space-evenly" />
        </view>
        <swiper class="swiper" :duration="500" style="height: 700rpx;" :current="current"
            @change="current = $event.detail.current">
            <swiper-item class="swiper-item">
                <view class="content">
                    <view class="screenplay-intro">剧本简介</view>
                    <!-- <view class="introduction">{{ scriptList.introduction }}</view> -->
                    <up-read-more class="introduction" showHeight="110rpx" toggle="true" closeText="展开"
                        :shadowStyle="{ backgroundImage: none }">
                        <rich-text :nodes="scriptList.introduction"></rich-text>
                    </up-read-more>
                    <view class="screenplay-intro">剧本角色</view>
                    <scroll-view class="role-scroll" scroll-x scroll-with-animation>
                        <view class="role">
                            <view class="role-image" v-for="(item, index) in scriptList.role" :key="id">
                                <up-image :src="item.cover" width="200rpx" height="280rpx" radius="20rpx" />
                                <view class="role-name">{{ item.name }}</view>
                            </view>
                        </view>
                    </scroll-view>
                </view>
            </swiper-item>
            <swiper-item class="swiper-item">
                <view class="content">
                    <h1>2</h1>
                </view>
            </swiper-item>
            <swiper-item class="swiper-item">
                <view class="content">
                    <h1>3</h1>
                </view>
            </swiper-item>
        </swiper>
    </view>
</template>
<style lang="scss" scoped>
@import "@/static/scss/leyout.scss";

.app-container {
    padding: $sp-page;

    .container-head {
        display: flex;
        align-items: center;
        gap: 20rpx;

        .container-head-right {
            height: 300rpx;

            .title {
                font-weight: 600;
            }

            .action-heat {
                margin-top: 20rpx;
                display: flex;
                padding: 2rpx 20rpx;
                background-color: #ffffff;
                border: 1px solid #ffffff;
                border-radius: 80rpx;
                width: 200rpx;


                .script-kill-heat {
                    font-size: 18rpx;
                    margin-right: 10rpx;
                }
            }

            .tag-item {
                margin-top: 20rpx;
                margin-right: 10rpx;
            }


            .script-kill-text {
                font-size: 26rpx;
                color: #000000;
                margin-top: 20rpx;
            }


            .brief-introduction {
                display: flex;
                align-items: flex-start;
                background: #ffffff;
                border-radius: 4rpx;
                margin-top: 20rpx;

                .introduction-text {
                    display: -webkit-box;
                    line-clamp: 2;
                    -webkit-line-clamp: 2;
                    -webkit-box-orient: vertical;
                    font-size: 20rpx;
                    overflow: hidden;
                    text-overflow: ellipsis;
                }
            }
        }
    }

    .container-middle {
        background: #ffffff;
        margin-top: 20rpx;
        // border-radius: 40rpx;
    }
}

.card-page-top {  
    .swiper {
        .content {
            padding: $sp-page;

            .screenplay-intro {
                font-size: 28rpx;
                margin-left: 20rpx;
                font-weight: bold;
            }

            .introduction {
                margin-top: 20rpx;
                font-size: 24rpx;
                padding: 20rpx;
                background: #f1f6ff;
            }

            .role-scroll {
                margin-top: 20rpx;
                overflow: hidden;
                white-space: nowrap;
                padding: 0 $sp-page;
            }

            .role {
                display: flex;
                gap: 20rpx;
                min-width: max-content;

                .role-image {
                    display: inline-flex;
                    flex-direction: column;
                    align-items: center;
                    width: 200rpx;

                    .role-name {
                        margin-top: 10rpx;
                        font-size: 24rpx;
                        text-align: center;
                    }
                }
            }
        }
    }
}
</style>