<script setup lang="ts">
import { defineProps } from 'vue';
interface Store {
    cover: string;
    id: number;
    name: string;
    rating: number;
    price: string;
    distance: string;
    currentPlayers: string;
    review: string;
}

defineProps<{
    store: Store;
}>();
</script>

<template>
    <view class="store-card">
        <view class="store-container">
            <view class="store-logo">
                <img :src="store.cover" class="imageclass" />
            </view>
            <view class="store-right">
                <view class="store-name">{{ store.name }}</view>
                <view class="store-rating-price">
                    <view class="store-rating">
                        评分：<up-rate :count="5" v-model="store.rating" :size="12" activeColor="#FDCB6D"
                            inactiveColor="#FEEBC7" readonly />
                    </view>
                    <view class="store-price">均￥<text class="price">{{ store.price }}</text>/人</view>
                </view>
                <view class="store-players-distance">
                    <view class="store-players">
                        <view class="pin-icon">拼</view>当场有<text class="gameNumber">{{ store.currentPlayers }}</text>
                        场在拼
                    </view>
                    <view class="store-distance">{{ store.distance }}km</view>
                </view>
                <view class="store-review">
                    <up-icon name="chat" color="blue" size="30rpx" />评价：{{ store.review }}
                </view>
            </view>
        </view>
    </view>
</template>

<style scoped lang="scss">
@import "@/static/scss/variables.module.scss";

.store-card {

    align-items: center;
    padding: $sp-card;
    background-color: white;

    .store-container {
        display: flex;
        align-items: center;
        gap: $sp-card;
        flex: 0;
        /* 图片与右侧内容之间的间距 */
        .store-logo {
            width: 33%;
            height: 100%;
            border-radius: $border-radius-small;

            .imageclass {
                height: 180rpx;
                width: 180rpx;
                 border-radius: $border-radius-small;
            }
        }

        .store-right {
            text-align: left;
            display: flex;
            flex-direction: column;
            gap: 12rpx;
            flex: 1;

            .store-name {
                font-size: 32rpx;
                color: $primary-text-color;
                font-weight: bold;
            }

            .store-rating-price {
                display: flex;
                justify-content: space-between;
                flex-direction: row;
                align-items: center;

                .store-rating {
                    display: flex;
                    font-size: 20rpx;
                    color: $secondary-text-color;
                    align-items: center;
                }

                .store-price {
                    font-size: 20rpx;
                    color: $auxiliary-color-red;
                    font-weight: bold;

                    .price {
                        font-size: 26rpx;
                    }
                }
            }

            .store-players-distance {
                display: flex;
                justify-content: space-between;

                .store-players {
                    display: flex;
                    align-items: center;
                    font-size: 20rpx;
                    color: $primary-text-color;
                    gap: 6rpx;

                    .pin-icon {
                        width: 26rpx;
                        height: 26rpx;
                        min-width: 26rpx;
                        min-height: 26rpx;
                        background-color: rgba($auxiliary-color-orange, 0.7);
                        /* 橙色背景 */
                        border-radius: 8rpx;
                        /* 圆形 */
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        color: white;
                        /* 文字颜色 */
                        font-size: 20rpx;
                        /* 字体大小 */

                    }

                    .gameNumber {
                        color: rgba($auxiliary-color-orange, 1);
                        font-weight: bold;
                    }
                }

                .store-distance {
                    font-size: 20rpx;
                    color: $primary-text-color;
                    font-weight: bold;
                }
            }

            .store-review {
                font-size: 20rpx;
                color: $primary-text-color;
                display: flex;
                align-items: center;
                gap: 6rpx;


            }
        }
    }

}
</style>