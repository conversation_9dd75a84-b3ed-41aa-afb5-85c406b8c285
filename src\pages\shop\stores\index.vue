<script setup lang="ts">
import { ref, reactive } from 'vue';
import banner05 from "@/static/images/banner/banner05.png";
import xuexiang from "@/static/images/cover/xuexiang.jpg";
import TagSelect from "@/components/jbs/tag-select.vue";
import Card from "@/components/jbs/card.vue";
import Man from "@/static/images/icon/man.png";
import Woman from "@/static/images/icon/woman.png";
import phoneIcon from "@/static/images/icon/phoneIcon.png";
const list6 = reactive([
    banner05,
    banner05,
    banner05,
]);
const scriptList = [
    {
        id: 5,
        title: '雪乡连环杀人事件',
        cover: xuexiang,
        tags: ['推理', '惊悚', '现代', '本格'],
        players: '3男3女',
        duration: '5小时',
        difficulty: '适中',
        address: '山西省太原市茂业一期光影剧本杀',
        distance: '10.0km',
        avatars: [
            'https://uview-plus.jiangruyi.com/uview-plus/album/1.jpg',
            'https://uview-plus.jiangruyi.com/uview-plus/album/2.jpg',
            'https://uview-plus.jiangruyi.com/uview-plus/album/3.jpg',
            'https://uview-plus.jiangruyi.com/uview-plus/album/4.jpg',
            'https://uview-plus.jiangruyi.com/uview-plus/album/7.jpg',
            'https://uview-plus.jiangruyi.com/uview-plus/album/6.jpg',
            'https://uview-plus.jiangruyi.com/uview-plus/album/5.jpg'
        ],
        waitingNumber: '2',
        startTime: '今天17:00',
        rating: 3,
        heat: 3,
        score: 9.2,
        alreadyNumber: 6,
        totalNumber: 8,
        price: 98,
        introduction: '年三十，北道河，村里出了个杀人魔。杀了一个又一个，最后一个杀老婆。七个小孩儿来串门，联起手来把案破。',
        paymentStatus: 2,
        wantPlayStatus: 1
    }
];
const currentScript = scriptList[0];
const currentNum = ref(0);
const value = ref(2);
const dmList = [{
    id: 1,
    name: '张三',
    avatar: 'https://img.yzcdn.cn/vant/cat.jpeg',
    sex: '男'
}, {
    id: 2,
    name: '张三',
    avatar: 'https://img.yzcdn.cn/vant/cat.jpeg',
    sex: '女'
}, {
    id: 3,
    name: '张三',
    avatar: 'https://img.yzcdn.cn/vant/cat.jpeg',
    sex: '女'
}, {
    id: 4,
    name: '张三',
    avatar: 'https://img.yzcdn.cn/vant/cat.jpeg',
    sex: '女'
}, {
    id: 4,
    name: '张三',
    avatar: 'https://img.yzcdn.cn/vant/cat.jpeg',
    sex: '女'
}, {
    id: 4,
    name: '张三',
    avatar: 'https://img.yzcdn.cn/vant/cat.jpeg',
    sex: '女'
}, {
    id: 4,
    name: '张三',
    avatar: 'https://img.yzcdn.cn/vant/cat.jpeg',
    sex: '女'
}
]
const List = [{
    id: 1,
    image: xuexiang,
    title: '你好',
    number: '1',
}, {
    id: 2,
    image: xuexiang,
    title: '你好',
    number: '1',
}, {
    image: xuexiang,
    title: '你好',
    number: '1',
}
]

function goToShop() {
    uni.switchTab({
        url: `/pages/shop/index`
    });
}
</script>

<template>
    <view class="app-container">
        <view class="top">
            <up-icon name="arrow-left" @click="goToShop()" color="white"></up-icon>
        </view>
        <up-swiper :list="list6" @change="(e: { current: number; }) => currentNum = e.current" :autoplay="false"
            indicatorStyle="right: 40rpx" class="carousel-chart" height="480rpx">

            <template #indicator>
                <view class="indicator-num">
                    <text class="indicator-num__text">{{ currentNum + 1 }}/{{ list6.length }}</text>
                </view>
            </template>
        </up-swiper>
        <view class="content">
            <up-card :show-head="false" :show-foot="false">
                <template #body>
                    <view class="card-body">
                        <up-image :src="banner05" shape="circle" width="120rpx" height="120rpx" />
                        <view class="right-body">
                            <view class="title">
                                <text class="store-name">怪诞事件簿</text>
                                <view class="collection">
                                    <up-icon name="star-fill" color="#ffa500" size="20"></up-icon>
                                    <text class="collection-text">收藏</text>
                                </view>
                            </view>
                            <view class="appraise-score">
                                <text class="score">4.5</text>
                                <up-rate :count="5" v-model="value" class="script-kill-rate" :activeColor="'#ffa500'"
                                    :size="13" :gutter="1" readonly />
                                <text>评价167条 人均99</text>
                            </view>
                        </view>
                    </view>
                    <view class="bottom">
                        <view class="address">
                            <up-icon name="map-fill" color="#3d6cfc" size="16"></up-icon>
                            <text class="address-text">山西省太原市茂业一期光影剧本杀</text>
                        </view>
                        <view class="phone-icon">
                            <view class="phone">
                                <up-icon name="phone-fill" color="#3d6cfc" size="16" />
                                <text class="phone-text">12535869541</text>
                            </view>
                            <view class="icon">
                                <!-- <up-icon name="phone-fill" color="#ffffff" size="20" class="icon-phone" /> -->
                                <up-image :src="phoneIcon" width="45rpx" height="45rpx" />
                                <up-icon name="weixin-circle-fill" color="#67C23A" size="30" />
                            </view>
                        </view>
                    </view>
                </template>
            </up-card>

            <view class="optional-script">
                <view class="optional-script-head">
                    <text class="optional-script-title">可选剧本</text>
                    <view class="optional-script-more">
                        <text class="optional-script-more-text">查看全部</text>
                        <up-icon name="more-circle-fill" color="#3d6cfc" size="20" class="icon-phone" />
                    </view>
                </view>
                <view class="optional-screen-play">
                    <view class="screen-play-card">
                        <view class="screen-play-card-image" v-for="(item, index) in List" :key="item.id">
                            <up-image :src="item.image" width="204rpx" height="300rpx" radius="20rpx" />
                            <text class="script-kill-title">{{ item.title }}</text>
                            <view class="action-heat">
                                <view class="script-kill-heat">热度:</view>
                                <up-rate :count="5" v-model="item.number" class="script-kill-rate" :gutter="2"
                                    :size="10" :activeColor="'#ffa500'" readonly />
                            </view>
                        </view>
                    </view>
                </view>
                <view class="script-kill-DM">
                    <view class="script-kill-DM-head">
                        <text class="script-kill-DM-head-text">店内DM</text>
                    </view>
                    <scroll-view class="DM-body" scroll-x>
                        <view class="script-kill-DM-body" v-for="(item, index) in dmList" :key="item.id">
                            <up-avatar :src="item.avatar" size="50" />
                            <view class="script-kill-DM-name">
                                <view class="script-kill-DM-name-text">{{ item.name }}</view>
                                <!-- <view class="script-kill-DM-name-icon">♂</view> -->
                                <up-image :src="Man" width="20rpx" height="20rpx" v-if="item.sex == '男'" />
                                <up-image :src="Woman" width="18rpx" height="18rpx" v-if="item.sex == '女'" />
                            </view>
                        </view>
                    </scroll-view>
                </view>
                <view class="DM-bottom">
                    <TagSelect class="ppd-l" />
                    <Card :script="currentScript" class="cmg-b" displayMode="HOME" />
                </view>
            </view>
        </view>
    </view>

</template>

<style lang="scss" scoped>
@import "@/static/scss/variables.module.scss";

.indicator {
    @include flex(row);
    justify-content: center;

    &__dot {
        height: 12rpx;
        width: 12rpx;
        border-radius: 200rpx;
        background-color: rgba(255, 255, 255, 0.35);
        margin: 0 10rpx;
        transition: background-color 0.3s;

        &--active {
            background-color: #ffffff;
        }
    }
}

.app-container {
    background: #edf1ff;

    .top {
        position: absolute;
        top: 15rpx;
        left: 15rpx;
        z-index: 999;
    }

    .carousel-chart {
       
        .indicator-num {
            padding: 4rpx 0;
            background-color: white;
            border-radius: 200rpx;
            width: 70rpx;
            @include flex;
            justify-content: center;

            &__text {
                color: #000000;
                font-size: 24rpx;
            }
        }
    }

    .content {
        margin-top: -200rpx;

        .card-body {
            display: flex;
            gap: 20rpx;

            .right-body {
                width: 100%;
                display: flex;
                flex-direction: column;
                justify-content: space-between;

                .title {
                    display: flex;
                    justify-content: space-between;
                    width: 100%;

                    .store-name {
                        color: #000000;
                        font-weight: bold;
                        font-size: 38rpx;
                    }

                    .collection {
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        width: 140rpx;
                        height: 50rpx;
                        border: 2rpx solid #fff9db;
                        background: #fff9db;
                        border-radius: 80rpx;

                        .collection-text {
                            color: #000000;
                            font-size: 24rpx;
                        }
                    }
                }


                .appraise-score {
                    display: flex;
                    align-items: baseline;
                    gap: 10rpx;

                    .score {
                        font-size: 36rpx;
                        color: #ffb400;
                    }
                }
            }
        }

        .bottom {
            margin-top: 10rpx;
            padding: 15rpx;
            border-radius: 15rpx;
            background: #edf1ff;
            gap: 10rpx;

            .address {
                display: flex;

                .address-text {
                    font-size: 24rpx;
                    color: #303133;
                }
            }

            .phone-icon {
                display: flex;
                justify-content: space-between;
                align-items: flex-end;

                .phone {
                    display: flex;

                    .phone-text {
                        font-size: 24rpx;
                        color: #303133;
                    }
                }

                .icon {
                    display: flex;
                    gap: 20rpx;
                    align-items: center;

                    .icon-phone {
                        background: #3d6cfc;
                        border-radius: 40rpx;
                        width: 45rpx;
                        height: 45rpx;
                    }
                }
            }
        }

        .optional-script {
            padding: $sp-page;

            .optional-script-head {
                display: flex;
                justify-content: space-between;

                .optional-script-title {
                    font-size: 32rpx;
                    font-weight: 600;
                }

                .optional-script-more {
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    gap: 20rpx;
                    width: 200rpx;
                    background: #ffffff;
                    border-radius: 30rpx;
                }
            }

            .optional-screen-play {
                margin-top: 20rpx;

                .screen-play-card {
                    display: flex;
                    flex: 4;
                    gap: 9rpx;
                    background: #ffffff;
                    border-radius: 20rpx;

                    .screen-play-card-image {
                        padding: 10rpx;

                        .script-kill-title {
                            display: flex;
                            margin-top: 5rpx;
                            justify-content: center;
                            font-weight: 600;
                            font-size: 26rpx;
                        }

                        .action-heat {
                            display: flex;
                            margin-top: 5rpx;
                            justify-content: center;
                            padding: 2rpx 20rpx;
                            background-color: #fff9db;
                            border: 1px solid #fff9db;
                            border-radius: 80rpx;
                            margin-bottom: 10rpx;

                            .script-kill-heat {
                                font-size: 16rpx;
                                margin-right: 10rpx;
                            }
                        }
                    }
                }
            }
        }

        .script-kill-DM {
            margin-top: 20rpx;
            padding: $sp-page;

            .script-kill-DM-head {
                .script-kill-DM-head-text {
                    font-size: 26rpx;
                    font-weight: 600;
                }
            }

            .DM-body {
                display: flex;
                gap: 20rpx;
                justify-content: flex-start;
                overflow: hidden;
                white-space: nowrap;

                .script-kill-DM-body {
                    margin-top: 20rpx;
                    display: inline-flex;
                    flex-direction: column;
                    align-items: center;
                    flex-shrink: 0;
                    width: 120rpx;

                    .script-kill-DM-name {
                        margin-top: 20rpx;
                        background: #ffffff;
                        border-radius: 80rpx;
                        width: 90rpx;
                        gap: 5rpx;
                        display: flex;
                        justify-content: center;
                        align-items: baseline;
                        font-size: 20rpx;
                    }
                }
            }
        }

        .DM-bottom {
            background: #ffffff;
            border-radius: 20rpx;

            .ppd-l {
                padding: 10rpx 10rpx 0 10rpx;
            }
        }
    }
}
</style>