@import "@/static/scss/variables.module.scss";

// 主区域背景色：大部分页面都使用这个背景色
.app-container {
    background-color: $background-color;
    min-height: 200rpx;
}

// 顶部蓝色渐变：首页、发现页、组局页
.top-container {
    width: 100%;
    background: $top-gradient-color;
    min-height: 100rpx;
}

// 卡片样式的界面的头部，例如：店内拼车、在线拼场、个人资料、排行榜 都有用到
.card-page-top {
    border-radius: $border-radius-large $border-radius-large 0 0;
    background-color: #ffffff;
    padding-top: $border-radius-large;
    padding-bottom: $sp-small;
}

// 文字下方具备重合效果的横块
.small-block {
    width: 60rpx;
    height: 20rpx;
    border-radius: $border-radius-large;
    background-color: transparentize($main-color, 0.4);
}

// 通过padding来设置容器右边距与页面边距相等
.ppd-l {
    padding-left: $sp-page;
}
.ppd-t {
    padding-top: calc(3 * $sp-page);
}

// 通过margin来设置容器下边距与卡片边距相等
.cmg-b {
    margin-bottom: $sp-card;
}

// 常见的flex布局
.flex-csb {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;
}

.flex-rsb {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
}

.flex-center {
    display: flex;
    justify-content: center;
    align-items: center;
}
