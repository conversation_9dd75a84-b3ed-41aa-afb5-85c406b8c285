// 01 色彩规范
// 主色
$main-color: #3d6cfc;
// 辅助色  橙色、红色、灰色
$auxiliary-color-red: #fd5a54;
$auxiliary-color-orange: #ffb400;
$auxiliary-color-gray: #e8f2ff;

$auxiliary-color-info: #909399; // 信息色

$primary-text-color: #303133; // 主要文字颜色
$secondary-text-color: $auxiliary-color-info; // 次要文字颜色
// 背景颜色
$background-color: #f1f6ff; // 页面背景色
// 顶部渐变色
$top-gradient-color: linear-gradient(180deg, #cddfff 0%, #f1f6ff 100%);

// 02 文字规范
// 中文：苹方  英文： San Francisco Display
$font-family-chinese: "PingFang SC", "Microsoft YaHei", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
$font-family-english: "SF UI Display", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
// 字体字号： 16px / 18px / 20px / 24px / 28px / 30px
// 使用场景： 角标&最小文字 / 注释文字 / 按钮文字 / 正文&导航 / 标题文字 / 特大号标题
$font-size-badge: 32rpx; // 角标
$font-size-small: 32rpx; // 最小文字
$font-size-note: 36rpx; // 注释文字
$font-size-button: 40rpx; // 按钮文字
$font-size-body: 48rpx; // 正文
$font-size-nav: 48rpx; // 导航
$font-size-title: 56rpx; // 标题文字
$font-size-xxl-title: 60rpx; // 特大号标题

// 03 布局规范
// 大卡片圆角20px、小瓷片圆角10px、最小圆角6px
$border-radius-large: 40rpx; // 大卡片
$border-radius-medium: 20rpx; // 小瓷片
$border-radius-small: 12rpx; // 最小圆角

//卡片上下边距48px  页边距30px  卡片边距20px 最小边距10px
$sp-vertical: 48rpx; // 卡片上下间距
$sp-page: 30rpx; // 页间距
$sp-card: 20rpx; // 卡片间距
$sp-small: 10rpx; // 最小间距
