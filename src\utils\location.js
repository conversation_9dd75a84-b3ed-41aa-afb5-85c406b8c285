/**
 * 地理位置相关工具类
 */

import modal from "@/plugins/modal";

let isIos
// #ifdef APP-PLUS
isIos = (plus.os.name == "iOS")
// #endif

// #ifdef MP-WEIXIN
const qqMapKey = 'RVMBZ-K4PK7-KJQXS-P5P74-WWSH7-FOBUD'
// #endif

/**
 * 地图类型常量
 */
export const MAP_TYPES = {
  AUTO: 'auto',
  BAIDU: 'baidu',
  GAODE: 'gaode',
  TENCENT: 'tencent'
};

const MAP_CONFIG = {
  // 腾讯地图配置
  tencent: {
    key: 'RVMBZ-K4PK7-KJQXS-P5P74-WWSH7-FOBUD',
    geocodeUrl: 'https://apis.map.qq.com/ws/geocoder/v1/',
  }
  // 添加其他地图服务配置
  // baidu: { ... },
  // gaode: { ... }
};


//========================核心代码=========================

/**
 * 完整的位置获取函数(定位功能)
 * @returns {Promise} 返回位置信息的Promise
 */
export async function getAddress() {
  try {
    // #ifdef APP-PLUS
    await checkSystemEnableLocation();
    // #endif

    // #ifdef MP-WEIXIN
    await checkLocationPermission();
    // #endif
    const locationData = await getLocationData();


    if (locationData.address) return locationData;

    // 如果没有地址信息，调用腾讯地图查找(小程序 查询不到address字段)
    const addressInfo = await getAddressByCoordinates(locationData.latitude, locationData.longitude);

    const { nation, province, city, district, street, street_number, other } = addressInfo.address_component;
    const { phone_area_code, otherInfo } = addressInfo.ad_info;
    const poiName = addressInfo.address;

    //重新组装一下地址信息
    const address = {
      country: nation,
      province: province,
      city: city,
      district: district,
      street: street,
      streetNum: street_number,
      poiName: poiName,
      cityCode: phone_area_code
    }
    locationData.address = address;

    return locationData;

  } catch (error) {
    modal.msg('获取地址信息失败');
    throw error;
  }
}


/**
 * 使用第三方地图应用导航(导航功能) 
 * @param {Object} options 位置信息
 * @param {string} mapType 地图类型: 'baidu', 'gaode', 'tencent'
 * 
 * latitude 纬度 longitude 经度 name 目标位置名称  address 地址的详细说明
 */
export function openThirdPartyMap(options, mapType = 'auto') {
  const { latitude, longitude, name = '目标位置', address = '' } = options;

  let url = '';

  // #ifdef APP-PLUS
  switch (mapType) {
    case 'baidu':
      url = `baidumap://map/direction?destination=${latitude},${longitude}&mode=driving&src=webapp.baidu.openAPIdemo`;
      break;
    case 'gaode':
      url = `iosamap://navi?sourceApplication=applicationName&poiname=${name}&lat=${latitude}&lon=${longitude}&dev=0`;
      break;
    case 'tencent':
      url = `qqmap://map/routeplan?type=drive&to=${name}&tocoord=${latitude},${longitude}`;
      break;
    default:
      // 自动选择可用的地图应用
      openMapNavigation(options);
      return;
  }

  plus.runtime.openURL(url, (err) => {
    console.error('打开第三方地图失败:', err);
    // 降级到系统地图
    openMapNavigation(options);
  });
  // #endif

  // #ifndef APP-PLUS
  openMapNavigation(options);
  // #endif
}


/**
 * 根据地址获取经纬度信息（地理编码）- 支持所有平台
 * @param {string} address 地址文字描述 ， 如 天安门广场
 * @param {string} provider 地图服务提供商，默认'tencent'
 * @returns {Promise} 返回包含经纬度的位置信息
 */
export async function getCoordinatesByAddress(address, provider = 'tencent') {
  return new Promise((resolve, reject) => {
    if (!address) {
      reject(new Error('地址不能为空'));
      return;
    }

    const config = MAP_CONFIG[provider];
    if (!config) {
      reject(new Error(`不支持的地图服务: ${provider}`));
      return;
    }

    
    uni.request({
      url: `${config.geocodeUrl}?address=${encodeURIComponent(address)}&key=${config.key}`,
      method: 'GET',
      timeout: 10000,
      success: (res) => {
        if (res.statusCode === 200 && res.data && res.data.status === 0) {
          const result = res.data.result;
          resolve(result);
        } else {
          reject(new Error(`地理编码失败: ${res.data?.message || '地址解析错误'}`));
        }
      },
      fail: (err) => {
        reject(new Error(`网络请求失败: ${err.errMsg || '未知错误'}`));
      }
    });
  });
}

//========================附属代码=========================

/**
 * 根据经纬度获取地址信息(小程序需设置 request合法域名：https://apis.map.qq.com)
 * 使用微信地图的逆地理编码(当前版本为低精度版，高精度版收费，需提交工单申请)
 */
async function getAddressByCoordinates(latitude, longitude) {
  return new Promise((resolve, reject) => {
    // #ifdef MP-WEIXIN
    wx.request({
      url: `https://apis.map.qq.com/ws/geocoder/v1/?location=${latitude},${longitude}&key=${qqMapKey}`,
      success: (res) => {
        if (res.data.status === 0) {
          resolve(res.data.result);
        } else {
          reject(new Error('地址解析失败'));
        }
      },
      fail: reject
    });
    // #endif
  });
}


/**
 * 检查位置权限 - app专用
 */
export function checkSystemEnableLocation() {
  if (isIos) {
    const cllocationManger = plus.ios.import("CLLocationManager");
    const result = cllocationManger.locationServicesEnabled();
    console.log("苹果系统定位开启:" + result);
    plus.ios.deleteObject(cllocationManger);
    return result;
  } else {
    const context = plus.android.importClass("android.content.Context");
    const locationManager = plus.android.importClass("android.location.LocationManager");
    const main = plus.android.runtimeMainActivity();
    const mainSvr = main.getSystemService(context.LOCATION_SERVICE);
    const result = mainSvr.isProviderEnabled(locationManager.GPS_PROVIDER);
    console.log("安卓系统定位开启:" + result);
    return result
  }
}



/**
 * 检查位置权限 - 微信小程序专用
 */
async function checkLocationPermission() {
  const settings = await getSetting();

  if (settings.authSetting['scope.userLocation']) {
    return; // 已授权，直接返回
  }

  try {
    await authorize('scope.userLocation');
  } catch (error) {
    // 授权失败，引导用户手动开启
    await showLocationPermissionModal();
  }
}

/**
 * 显示位置权限弹窗并处理用户选择
 */
async function showLocationPermissionModal() {
  const modalResult = await showModal({
    title: '提示',
    content: '需要获取您的地理位置，请确认授权'
  });

  if (!modalResult.confirm) {
    throw new Error('用户拒绝授权');
  }

  const settingResult = await openSetting();
  if (!settingResult.authSetting['scope.userLocation']) {
    throw new Error('用户拒绝授权');
  }
}

function getSetting() {
  return new Promise((resolve, reject) => {
    uni.getSetting({
      success: resolve,
      fail: reject
    });
  });
}

function authorize(scope) {
  return new Promise((resolve, reject) => {
    uni.authorize({
      scope,
      success: resolve,
      fail: reject
    });
  });
}

function showModal(options) {
  return new Promise((resolve) => {
    uni.showModal({
      ...options,
      success: resolve
    });
  });
}

function openSetting() {
  return new Promise((resolve) => {
    uni.openSetting({
      success: resolve
    });
  });
}


/**
 * 获取定位信息
 */
function getLocationData() {
  return new Promise((resolve, reject) => {
    uni.getLocation({
      type: 'gcj02',
      geocode: true,
      success: (res) => {
        resolve(res);
      },
      fail: (err) => {
        reject(err);
      }
    });
  });
}
/**
 * 打开地图导航到指定位置   (打开统默认的地图)
 * @param {Object} options 位置信息
 * @param {number} options.latitude 纬度
 * @param {number} options.longitude 经度
 * @param {string} options.name 位置名称
 * @param {string} options.address 详细地址
 */
export function openMapNavigation(options) {
  const { latitude, longitude, name = '目标位置', address = '' } = options;

  uni.openLocation({
    latitude: parseFloat(latitude),
    longitude: parseFloat(longitude),
    name: name,
    address: address,
    success: () => {
      console.log('打开地图成功');
    },
    fail: (err) => {
      modal.msg('打开地图失败')
      console.error('打开地图失败:', err);
    }
  });
}

/**
 * 微信小程序地图导航
 * @param {Object} options 位置信息
 */
export function openWeixinMap(options) {
  const { latitude, longitude, name = '目标位置', address = '' } = options;

  // #ifdef MP-WEIXIN
  uni.openLocation({
    latitude: parseFloat(latitude),
    longitude: parseFloat(longitude),
    name: name,
    address: address,
    scale: 18,
    success: () => {
      console.log('打开微信地图成功');
    },
    fail: (err) => {
      modal.msg('打开微信地图失败')
      console.error('打开微信地图失败:', err);
    }
  });
  // #endif
}



export default {
  MAP_TYPES,
  getAddress,
  openMapNavigation,
  openThirdPartyMap,
  openWeixinMap,
  getCoordinatesByAddress
};