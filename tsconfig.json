{"compilerOptions": {"noImplicitAny": true, "importHelpers": true, "strict": true, "forceConsistentCasingInFileNames": true, "downlevelIteration": true, "outDir": "dist/compiled", "baseUrl": "./", "allowSyntheticDefaultImports": true, "allowJs": true, "jsx": "preserve", "moduleResolution": "node", "module": "ES2022", "lib": ["ES2020", "dom"], "declaration": true, "target": "ES2020", "paths": {"@": ["src"], "@/*": ["src/*"], "/@/*": ["src/*"], "vue": ["node_modules/vue"], "tslib": ["node_modules/tslib/tslib.d.ts"]}}, "extends": "./temp/tsconfig.cocos.json", "include": ["assets/**/*.ts"], "exclude": ["build", "cache", "library", "node_modules", "temp", ".cocos", "**/*.js", "**/*.d.ts"]}